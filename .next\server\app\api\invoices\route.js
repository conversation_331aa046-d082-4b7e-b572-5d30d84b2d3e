"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/invoices/route";
exports.ids = ["app/api/invoices/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_invoices_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/invoices/route.ts */ \"(rsc)/./app/api/invoices/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/invoices/route\",\n        pathname: \"/api/invoices\",\n        filename: \"route\",\n        bundlePath: \"app/api/invoices/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\invoices\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_invoices_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/invoices/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/invoices/route.ts":
/*!***********************************!*\
  !*** ./app/api/invoices/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for invoice items\nconst invoiceItemSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Description is required\"),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(1, \"Quantity must be at least 1\"),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Unit price must be positive\"),\n    discount: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0)\n});\n// Validation schema for invoice creation/update\nconst invoiceSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    invoiceNumber: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Customer is required\"),\n    quotationId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"DRAFT\",\n        \"SENT\",\n        \"VIEWED\",\n        \"PAID\",\n        \"OVERDUE\",\n        \"CANCELLED\"\n    ]).default(\"DRAFT\"),\n    issueDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    dueDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    items: zod__WEBPACK_IMPORTED_MODULE_4__.array(invoiceItemSchema).min(1, \"At least one item is required\"),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0),\n    discountType: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"PERCENTAGE\",\n        \"FIXED\"\n    ]).optional().default(\"PERCENTAGE\"),\n    discountValue: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).optional().default(0),\n    paymentTerms: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    paymentMethod: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable()\n});\n// GET /api/invoices - List invoices with filtering and pagination\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const search = searchParams.get(\"search\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const customerId = searchParams.get(\"customerId\") || \"\";\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            companyId: session.user.companyId || undefined\n        };\n        if (search) {\n            where.OR = [\n                {\n                    invoiceNumber: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    customer: {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                },\n                {\n                    customer: {\n                        company: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ];\n        }\n        if (status) {\n            where.status = status;\n        }\n        if (customerId) {\n            where.customerId = customerId;\n        }\n        // Get invoices with pagination\n        const [invoices, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findMany({\n                where,\n                skip,\n                take: limit,\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: {\n                        orderBy: {\n                            createdAt: \"asc\"\n                        }\n                    },\n                    _count: {\n                        select: {\n                            activities: true,\n                            payments: true\n                        }\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count({\n                where\n            })\n        ]);\n        // Calculate totals for each invoice\n        const invoicesWithTotals = invoices.map((invoice)=>{\n            const subtotal = invoice.items.reduce((sum, item)=>{\n                const itemTotal = item.quantity * item.unitPrice;\n                const discountAmount = itemTotal * item.discount / 100;\n                const afterDiscount = itemTotal - discountAmount;\n                const taxAmount = afterDiscount * item.taxRate / 100;\n                return sum + afterDiscount + taxAmount;\n            }, 0);\n            let total = subtotal;\n            if (invoice.discountType === \"PERCENTAGE\") {\n                total = subtotal - subtotal * invoice.discountValue / 100;\n            } else {\n                total = subtotal - invoice.discountValue;\n            }\n            const finalTaxAmount = total * invoice.taxRate / 100;\n            const finalTotal = total + finalTaxAmount;\n            return {\n                ...invoice,\n                subtotal: Math.round(subtotal * 100) / 100,\n                total: Math.round(finalTotal * 100) / 100\n            };\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            invoices: invoicesWithTotals,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching invoices:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch invoices\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/invoices - Create new invoice\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = invoiceSchema.parse(body);\n        // Generate invoice number if not provided\n        let invoiceNumber = validatedData.invoiceNumber;\n        if (!invoiceNumber) {\n            const currentYear = new Date().getFullYear();\n            const lastInvoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findFirst({\n                where: {\n                    companyId: session.user.companyId || undefined,\n                    invoiceNumber: {\n                        startsWith: `INV-${currentYear}-`\n                    }\n                },\n                orderBy: {\n                    invoiceNumber: \"desc\"\n                }\n            });\n            let nextNumber = 1;\n            if (lastInvoice) {\n                const lastNumber = parseInt(lastInvoice.invoiceNumber.split(\"-\")[2]);\n                nextNumber = lastNumber + 1;\n            }\n            invoiceNumber = `INV-${currentYear}-${nextNumber.toString().padStart(4, \"0\")}`;\n        }\n        // Prepare invoice data\n        const invoiceData = {\n            ...validatedData,\n            invoiceNumber,\n            companyId: session.user.companyId,\n            createdById: session.user.id\n        };\n        if (validatedData.issueDate) {\n            invoiceData.issueDate = new Date(validatedData.issueDate);\n        } else {\n            invoiceData.issueDate = new Date();\n        }\n        if (validatedData.dueDate) {\n            invoiceData.dueDate = new Date(validatedData.dueDate);\n        } else {\n            // Default to 30 days from issue date\n            const dueDate = new Date(invoiceData.issueDate);\n            dueDate.setDate(dueDate.getDate() + 30);\n            invoiceData.dueDate = dueDate;\n        }\n        // Create invoice with items in a transaction\n        const invoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            const newInvoice = await tx.invoice.create({\n                data: {\n                    ...invoiceData,\n                    items: {\n                        create: validatedData.items.map((item)=>({\n                                ...item,\n                                companyId: session.user.companyId\n                            }))\n                    }\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: {\n                        orderBy: {\n                            createdAt: \"asc\"\n                        }\n                    }\n                }\n            });\n            // Log activity\n            await tx.activity.create({\n                data: {\n                    type: \"NOTE\",\n                    title: \"Invoice Created\",\n                    description: `Invoice \"${invoiceNumber}\" was created`,\n                    invoiceId: newInvoice.id,\n                    customerId: newInvoice.customerId,\n                    quotationId: newInvoice.quotationId,\n                    companyId: session.user.companyId,\n                    createdById: session.user.id\n                }\n            });\n            // If created from quotation, update quotation status\n            if (newInvoice.quotationId) {\n                await tx.quotation.update({\n                    where: {\n                        id: newInvoice.quotationId\n                    },\n                    data: {\n                        status: \"ACCEPTED\"\n                    }\n                });\n                await tx.activity.create({\n                    data: {\n                        type: \"STATUS_CHANGE\",\n                        title: \"Quotation Converted to Invoice\",\n                        description: `Quotation was converted to invoice \"${invoiceNumber}\"`,\n                        quotationId: newInvoice.quotationId,\n                        invoiceId: newInvoice.id,\n                        customerId: newInvoice.customerId,\n                        companyId: session.user.companyId,\n                        createdById: session.user.id\n                    }\n                });\n            }\n            return newInvoice;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(invoice, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error creating invoice:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create invoice\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/invoices/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    },\n                    include: {\n                        company: true,\n                        ownedCompany: true\n                    }\n                });\n                if (!user || !user.password) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                // Update last login\n                await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.update({\n                    where: {\n                        id: user.id\n                    },\n                    data: {\n                        lastLoginAt: new Date(),\n                        loginCount: {\n                            increment: 1\n                        }\n                    }\n                });\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    companyId: user.companyId,\n                    company: user.company || user.ownedCompany\n                };\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.companyId = user.companyId;\n                token.company = user.company;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                session.user.company = token.company;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
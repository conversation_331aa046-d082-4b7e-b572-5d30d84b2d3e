"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leads/[id]/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/save.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Save; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Save = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Save\", [\n  [\"path\", { d: \"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z\", key: \"1owoqh\" }],\n  [\"polyline\", { points: \"17 21 17 13 7 13 7 21\", key: \"1md35c\" }],\n  [\"polyline\", { points: \"7 3 7 8 15 8\", key: \"8nz8an\" }]\n]);\n\n\n//# sourceMappingURL=save.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2F2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGFBQWEsZ0VBQWdCO0FBQzdCLGFBQWEscUZBQXFGO0FBQ2xHLGlCQUFpQixnREFBZ0Q7QUFDakUsaUJBQWlCLHVDQUF1QztBQUN4RDs7QUFFMkI7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9zYXZlLmpzP2JjNjYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMzAzLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBTYXZlID0gY3JlYXRlTHVjaWRlSWNvbihcIlNhdmVcIiwgW1xuICBbXCJwYXRoXCIsIHsgZDogXCJNMTkgMjFINWEyIDIgMCAwIDEtMi0yVjVhMiAyIDAgMCAxIDItMmgxMWw1IDV2MTFhMiAyIDAgMCAxLTIgMnpcIiwga2V5OiBcIjFvd29xaFwiIH1dLFxuICBbXCJwb2x5bGluZVwiLCB7IHBvaW50czogXCIxNyAyMSAxNyAxMyA3IDEzIDcgMjFcIiwga2V5OiBcIjFtZDM1Y1wiIH1dLFxuICBbXCJwb2x5bGluZVwiLCB7IHBvaW50czogXCI3IDMgNyA4IDE1IDhcIiwga2V5OiBcIjhuejhhblwiIH1dXG5dKTtcblxuZXhwb3J0IHsgU2F2ZSBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zYXZlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/dashboard/leads/[id]/page.tsx":
/*!*******************************************!*\
  !*** ./app/dashboard/leads/[id]/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeadDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_leads_lead_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/leads/lead-form */ \"(app-pages-browser)/./components/leads/lead-form.tsx\");\n/* harmony import */ var _components_leads_activity_timeline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/leads/activity-timeline */ \"(app-pages-browser)/./components/leads/activity-timeline.tsx\");\n/* harmony import */ var _components_leads_lead_notes__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/leads/lead-notes */ \"(app-pages-browser)/./components/leads/lead-notes.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LeadDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [lead, setLead] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showEditForm, setShowEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const fetchLead = async ()=>{\n        try {\n            const response = await fetch(\"/api/leads/\".concat(params.id));\n            if (!response.ok) {\n                if (response.status === 404) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Lead not found\");\n                    router.push(\"/dashboard/leads\");\n                    return;\n                }\n                throw new Error(\"Failed to fetch lead\");\n            }\n            const data = await response.json();\n            setLead(data.lead);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Failed to load lead details\");\n            console.error(\"Error fetching lead:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (params.id) {\n            fetchLead();\n        }\n    }, [\n        params.id\n    ]);\n    const handleDelete = async ()=>{\n        if (!lead || !confirm('Are you sure you want to delete \"'.concat(lead.firstName, \" \").concat(lead.lastName, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/leads/\".concat(lead.id), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to delete lead\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"Lead deleted successfully\");\n            router.push(\"/dashboard/leads\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__.toast.error(error instanceof Error ? error.message : \"Failed to delete lead\");\n        }\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"NEW\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    children: \"New\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 16\n                }, this);\n            case \"CONTACTED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"Contacted\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 16\n                }, this);\n            case \"QUALIFIED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"Qualified\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 16\n                }, this);\n            case \"PROPOSAL\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"Proposal\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, this);\n            case \"NEGOTIATION\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-orange-100 text-orange-800\",\n                    children: \"Negotiation\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 16\n                }, this);\n            case \"CLOSED_WON\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"Closed Won\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 16\n                }, this);\n            case \"CLOSED_LOST\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"destructive\",\n                    children: \"Closed Lost\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getPriorityBadge = (priority)=>{\n        switch(priority){\n            case \"LOW\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    className: \"text-xs\",\n                    children: \"Low\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 16\n                }, this);\n            case \"MEDIUM\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-blue-100 text-blue-800 text-xs\",\n                    children: \"Medium\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 16\n                }, this);\n            case \"HIGH\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-orange-100 text-orange-800 text-xs\",\n                    children: \"High\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 16\n                }, this);\n            case \"URGENT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"destructive\",\n                    className: \"text-xs\",\n                    children: \"Urgent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    className: \"text-xs\",\n                    children: priority\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    if (!lead) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Lead Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mb-4\",\n                        children: \"The lead you're looking for doesn't exist.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                            href: \"/dashboard/leads\",\n                            children: \"Back to Leads\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                    href: \"/dashboard/leads\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Leads\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: [\n                                            lead.firstName,\n                                            \" \",\n                                            lead.lastName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: lead.title && lead.companyName ? \"\".concat(lead.title, \" at \").concat(lead.companyName) : lead.title || lead.companyName || \"Lead Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setShowEditForm(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"destructive\",\n                                onClick: handleDelete,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            getStatusBadge(lead.status)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Priority\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            getPriorityBadge(lead.priority)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: [\n                                                    lead.score,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-5 w-5 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Budget\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: lead.budget ? \"$\".concat(lead.budget.toLocaleString()) : \"Not specified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                value: activeTab,\n                onValueChange: setActiveTab,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"overview\",\n                                children: \"Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"activities\",\n                                children: [\n                                    \"Activities (\",\n                                    lead._count.activities,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"notes\",\n                                children: [\n                                    \"Notes (\",\n                                    lead._count.leadNotes,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"tasks\",\n                                children: [\n                                    \"Tasks (\",\n                                    lead._count.tasks,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"documents\",\n                                children: [\n                                    \"Documents (\",\n                                    lead._count.documents,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Contact Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: lead.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this),\n                                                lead.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Phone\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: lead.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this),\n                                                lead.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Website\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: lead.website,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"font-medium text-blue-600 hover:underline\",\n                                                                    children: lead.website\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Company Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                lead.companyName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Company\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: lead.companyName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, this),\n                                                lead.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Job Title\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: lead.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this),\n                                                lead.industry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Industry\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: lead.industry\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this),\n                                                lead.companySize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Company Size\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: lead.companySize\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"activities\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leads_activity_timeline__WEBPACK_IMPORTED_MODULE_9__.ActivityTimeline, {\n                            leadId: lead.id\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"notes\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leads_lead_notes__WEBPACK_IMPORTED_MODULE_10__.LeadNotes, {\n                            leadId: lead.id\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"tasks\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"Related Tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Task management will be implemented next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"documents\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"Documents\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Document management will be implemented next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            showEditForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leads_lead_form__WEBPACK_IMPORTED_MODULE_8__.LeadForm, {\n                isOpen: showEditForm,\n                onClose: ()=>setShowEditForm(false),\n                onSuccess: ()=>{\n                    setShowEditForm(false);\n                    fetchLead();\n                },\n                lead: lead,\n                mode: \"edit\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(LeadDetailPage, \"Wyn3U8y+rCowsEfTjyUW2mkRKwA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession\n    ];\n});\n_c = LeadDetailPage;\nvar _c;\n$RefreshReg$(_c, \"LeadDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/leads/[id]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/leads/lead-notes.tsx":
/*!*****************************************!*\
  !*** ./components/leads/lead-notes.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LeadNotes: function() { return /* binding */ LeadNotes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,MessageSquare,MoreHorizontal,Plus,Save,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,MessageSquare,MoreHorizontal,Plus,Save,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,MessageSquare,MoreHorizontal,Plus,Save,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,MessageSquare,MoreHorizontal,Plus,Save,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,MessageSquare,MoreHorizontal,Plus,Save,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,MessageSquare,MoreHorizontal,Plus,Save,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,MessageSquare,MoreHorizontal,Plus,Save,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,MessageSquare,MoreHorizontal,Plus,Save,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,MessageSquare,MoreHorizontal,Plus,Save,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ LeadNotes auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction LeadNotes(param) {\n    let { leadId } = param;\n    _s();\n    const [notes, setNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingNote, setEditingNote] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        isPrivate: false\n    });\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchNotes = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/leads/\".concat(leadId, \"/notes\"));\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch notes\");\n            }\n            const data = await response.json();\n            setNotes(data.notes);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to load notes\");\n            console.error(\"Error fetching notes:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (leadId) {\n            fetchNotes();\n        }\n    }, [\n        leadId\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.title.trim() || !formData.content.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Title and content are required\");\n            return;\n        }\n        try {\n            setSaving(true);\n            const url = editingNote ? \"/api/lead-notes/\".concat(editingNote.id) : \"/api/leads/\".concat(leadId, \"/notes\");\n            const method = editingNote ? \"PUT\" : \"POST\";\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(formData)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to save note\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Note \".concat(editingNote ? \"updated\" : \"created\", \" successfully\"));\n            setShowForm(false);\n            setEditingNote(null);\n            setFormData({\n                title: \"\",\n                content: \"\",\n                isPrivate: false\n            });\n            fetchNotes();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(error instanceof Error ? error.message : \"Failed to save note\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleEdit = (note)=>{\n        setEditingNote(note);\n        setFormData({\n            title: note.title,\n            content: note.content,\n            isPrivate: note.isPrivate\n        });\n        setShowForm(true);\n    };\n    const handleDelete = async (noteId)=>{\n        if (!confirm(\"Are you sure you want to delete this note?\")) return;\n        try {\n            const response = await fetch(\"/api/lead-notes/\".concat(noteId), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete note\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Note deleted successfully\");\n            fetchNotes();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to delete note\");\n            console.error(\"Error deleting note:\", error);\n        }\n    };\n    const handleCancel = ()=>{\n        setShowForm(false);\n        setEditingNote(null);\n        setFormData({\n            title: \"\",\n            content: \"\",\n            isPrivate: false\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                lineNumber: 167,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Lead Notes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>setShowForm(true),\n                        size: \"sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Note\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: notes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"py-8 text-center text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No notes found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Add your first note to get started\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, this) : notes.map((note)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-base\",\n                                                    children: note.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-sm text-gray-500 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: note.createdBy.name || note.createdBy.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: new Date(note.createdAt).toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        note.isPrivate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs\",\n                                                            children: \"Private\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"h-8 w-8 p-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuContent, {\n                                                    align: \"end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                            onClick: ()=>handleEdit(note),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Edit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                            onClick: ()=>handleDelete(note.id),\n                                                            className: \"text-red-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Delete\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"whitespace-pre-wrap text-sm text-gray-700\",\n                                        children: note.content\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this),\n                                    note.updatedAt !== note.createdAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-2\",\n                                        children: [\n                                            \"Last updated: \",\n                                            new Date(note.updatedAt).toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, note.id, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n                open: showForm,\n                onOpenChange: handleCancel,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                                children: editingNote ? \"Edit Note\" : \"Add New Note\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"title\",\n                                            children: \"Title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"title\",\n                                            value: formData.title,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    title: e.target.value\n                                                }),\n                                            placeholder: \"Enter note title\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"content\",\n                                            children: \"Content\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            id: \"content\",\n                                            value: formData.content,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    content: e.target.value\n                                                }),\n                                            placeholder: \"Enter note content\",\n                                            rows: 6,\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"isPrivate\",\n                                            checked: formData.isPrivate,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    isPrivate: e.target.checked\n                                                }),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"isPrivate\",\n                                            className: \"text-sm\",\n                                            children: \"Private note (only visible to you)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-2 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: handleCancel,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Cancel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"submit\",\n                                            disabled: saving,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_MessageSquare_MoreHorizontal_Plus_Save_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this),\n                                                saving ? \"Saving...\" : editingNote ? \"Update Note\" : \"Save Note\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\components\\\\leads\\\\lead-notes.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n_s(LeadNotes, \"6rIa2Lp4R803nl5xSVPIXnYF614=\");\n_c = LeadNotes;\nvar _c;\n$RefreshReg$(_c, \"LeadNotes\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/leads/lead-notes.tsx\n"));

/***/ })

});
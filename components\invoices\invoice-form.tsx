'use client'

import { useState, useEffect } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Plus, Trash2, Calculator } from 'lucide-react'
import { toast } from 'react-hot-toast'

const invoiceItemSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  discount: z.number().min(0).max(100).default(0),
  taxRate: z.number().min(0).max(100).default(0)
})

const invoiceSchema = z.object({
  customerId: z.string().min(1, 'Customer is required'),
  quotationId: z.string().optional(),
  status: z.enum(['DRAFT', 'SENT', 'VIEWED', 'PAID', 'OVERDUE', 'CANCELLED']).default('DRAFT'),
  issueDate: z.string().optional(),
  dueDate: z.string().optional(),
  terms: z.string().optional(),
  notes: z.string().optional(),
  items: z.array(invoiceItemSchema).min(1, 'At least one item is required'),
  taxRate: z.number().min(0).max(100).default(0),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).default('PERCENTAGE'),
  discountValue: z.number().min(0).default(0),
  paymentTerms: z.string().optional(),
  paymentMethod: z.string().optional()
})

type InvoiceFormData = z.infer<typeof invoiceSchema>

interface InvoiceFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  invoice?: any
  mode: 'create' | 'edit'
  preselectedCustomerId?: string
  preselectedQuotationId?: string
}

export function InvoiceForm({ 
  isOpen, 
  onClose, 
  onSuccess, 
  invoice, 
  mode, 
  preselectedCustomerId,
  preselectedQuotationId 
}: InvoiceFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [customers, setCustomers] = useState<any[]>([])
  const [quotations, setQuotations] = useState<any[]>([])

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    control,
    setValue
  } = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceSchema),
    defaultValues: invoice ? {
      customerId: invoice.customerId || preselectedCustomerId || '',
      quotationId: invoice.quotationId || preselectedQuotationId || '',
      status: invoice.status || 'DRAFT',
      issueDate: invoice.issueDate ? new Date(invoice.issueDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      dueDate: invoice.dueDate ? new Date(invoice.dueDate).toISOString().split('T')[0] : (() => {
        const date = new Date()
        date.setDate(date.getDate() + 30)
        return date.toISOString().split('T')[0]
      })(),
      terms: invoice.terms || '',
      notes: invoice.notes || '',
      items: invoice.items || [{ description: '', quantity: 1, unitPrice: 0, discount: 0, taxRate: 0 }],
      taxRate: invoice.taxRate || 0,
      discountType: invoice.discountType || 'PERCENTAGE',
      discountValue: invoice.discountValue || 0,
      paymentTerms: invoice.paymentTerms || 'Net 30',
      paymentMethod: invoice.paymentMethod || ''
    } : {
      status: 'DRAFT',
      customerId: preselectedCustomerId || '',
      quotationId: preselectedQuotationId || '',
      issueDate: new Date().toISOString().split('T')[0],
      dueDate: (() => {
        const date = new Date()
        date.setDate(date.getDate() + 30)
        return date.toISOString().split('T')[0]
      })(),
      items: [{ description: '', quantity: 1, unitPrice: 0, discount: 0, taxRate: 0 }],
      taxRate: 0,
      discountType: 'PERCENTAGE',
      discountValue: 0,
      paymentTerms: 'Net 30'
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items'
  })

  const watchedItems = watch('items')
  const watchedTaxRate = watch('taxRate')
  const watchedDiscountType = watch('discountType')
  const watchedDiscountValue = watch('discountValue')

  // Fetch customers and quotations
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [customersRes, quotationsRes] = await Promise.all([
          fetch('/api/customers?limit=100'),
          fetch('/api/quotations?limit=100&status=ACCEPTED')
        ])

        if (customersRes.ok) {
          const customersData = await customersRes.json()
          setCustomers(customersData.customers)
        }

        if (quotationsRes.ok) {
          const quotationsData = await quotationsRes.json()
          setQuotations(quotationsData.quotations)
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    }

    if (isOpen) {
      fetchData()
    }
  }, [isOpen])

  // Load quotation items when quotation is selected
  const watchedQuotationId = watch('quotationId')
  useEffect(() => {
    if (watchedQuotationId && mode === 'create') {
      const loadQuotationItems = async () => {
        try {
          const response = await fetch(`/api/quotations/${watchedQuotationId}`)
          if (response.ok) {
            const quotation = await response.json()
            if (quotation.items && quotation.items.length > 0) {
              // Clear existing items and add quotation items
              setValue('items', quotation.items.map((item: any) => ({
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                discount: item.discount,
                taxRate: item.taxRate
              })))
              setValue('taxRate', quotation.taxRate)
              setValue('discountType', quotation.discountType)
              setValue('discountValue', quotation.discountValue)
              setValue('terms', quotation.terms || '')
            }
          }
        } catch (error) {
          console.error('Error loading quotation items:', error)
        }
      }
      loadQuotationItems()
    }
  }, [watchedQuotationId, mode, setValue])

  // Calculate totals
  const calculateTotals = () => {
    const subtotal = watchedItems.reduce((sum, item) => {
      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0)
      const discountAmount = (itemTotal * (item.discount || 0)) / 100
      const afterDiscount = itemTotal - discountAmount
      const taxAmount = (afterDiscount * (item.taxRate || 0)) / 100
      return sum + afterDiscount + taxAmount
    }, 0)

    let total = subtotal
    if (watchedDiscountType === 'PERCENTAGE') {
      total = subtotal - (subtotal * (watchedDiscountValue || 0)) / 100
    } else {
      total = subtotal - (watchedDiscountValue || 0)
    }

    const finalTaxAmount = (total * (watchedTaxRate || 0)) / 100
    const finalTotal = total + finalTaxAmount

    return {
      subtotal: Math.round(subtotal * 100) / 100,
      total: Math.round(finalTotal * 100) / 100,
      taxAmount: Math.round(finalTaxAmount * 100) / 100,
      discountAmount: watchedDiscountType === 'PERCENTAGE' 
        ? Math.round((subtotal * (watchedDiscountValue || 0) / 100) * 100) / 100
        : (watchedDiscountValue || 0)
    }
  }

  const totals = calculateTotals()

  const addItem = () => {
    append({ description: '', quantity: 1, unitPrice: 0, discount: 0, taxRate: 0 })
  }

  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  const onSubmit = async (data: InvoiceFormData) => {
    setIsLoading(true)
    try {
      const url = mode === 'create' ? '/api/invoices' : `/api/invoices/${invoice.id}`
      const method = mode === 'create' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save invoice')
      }

      toast.success(`Invoice ${mode === 'create' ? 'created' : 'updated'} successfully`)
      reset()
      onSuccess()
      onClose()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    reset()
    onClose()
  }

  const statusOptions = [
    { value: 'DRAFT', label: 'Draft' },
    { value: 'SENT', label: 'Sent' },
    { value: 'VIEWED', label: 'Viewed' },
    { value: 'PAID', label: 'Paid' },
    { value: 'OVERDUE', label: 'Overdue' },
    { value: 'CANCELLED', label: 'Cancelled' }
  ]

  const paymentTermsOptions = [
    'Net 15',
    'Net 30',
    'Net 45',
    'Net 60',
    'Due on Receipt',
    'COD (Cash on Delivery)',
    '2/10 Net 30',
    '1/15 Net 30'
  ]

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Create New Invoice' : 'Edit Invoice'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? 'Create a new invoice with items, pricing, and payment terms.'
              : 'Update the invoice information and items.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="customerId">Customer *</Label>
              <select
                id="customerId"
                {...register('customerId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a customer</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} {customer.company && `(${customer.company})`}
                  </option>
                ))}
              </select>
              {errors.customerId && (
                <p className="text-sm text-red-600 mt-1">{errors.customerId.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="quotationId">Related Quotation</Label>
              <select
                id="quotationId"
                {...register('quotationId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a quotation (optional)</option>
                {quotations.map((quotation) => (
                  <option key={quotation.id} value={quotation.id}>
                    {quotation.quotationNumber} - {quotation.title}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                {...register('status')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="paymentTerms">Payment Terms</Label>
              <select
                id="paymentTerms"
                {...register('paymentTerms')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select payment terms</option>
                {paymentTermsOptions.map((term) => (
                  <option key={term} value={term}>
                    {term}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="issueDate">Issue Date</Label>
              <Input
                id="issueDate"
                type="date"
                {...register('issueDate')}
              />
            </div>

            <div>
              <Label htmlFor="dueDate">Due Date</Label>
              <Input
                id="dueDate"
                type="date"
                {...register('dueDate')}
              />
            </div>
          </div>

          {/* Items Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Items</h3>
              <Button type="button" onClick={addItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>

            <div className="space-y-4">
              {fields.map((field, index) => (
                <div key={field.id} className="grid grid-cols-12 gap-2 items-end p-4 border rounded-lg">
                  <div className="col-span-4">
                    <Label htmlFor={`items.${index}.description`}>Description *</Label>
                    <Input
                      {...register(`items.${index}.description`)}
                      placeholder="Item description"
                    />
                    {errors.items?.[index]?.description && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.items[index]?.description?.message}
                      </p>
                    )}
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor={`items.${index}.quantity`}>Qty *</Label>
                    <Input
                      type="number"
                      min="1"
                      {...register(`items.${index}.quantity`, { valueAsNumber: true })}
                      placeholder="1"
                    />
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor={`items.${index}.unitPrice`}>Unit Price *</Label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      {...register(`items.${index}.unitPrice`, { valueAsNumber: true })}
                      placeholder="0.00"
                    />
                  </div>

                  <div className="col-span-1">
                    <Label htmlFor={`items.${index}.discount`}>Disc %</Label>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      {...register(`items.${index}.discount`, { valueAsNumber: true })}
                      placeholder="0"
                    />
                  </div>

                  <div className="col-span-1">
                    <Label htmlFor={`items.${index}.taxRate`}>Tax %</Label>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      {...register(`items.${index}.taxRate`, { valueAsNumber: true })}
                      placeholder="0"
                    />
                  </div>

                  <div className="col-span-1">
                    <Label>Total</Label>
                    <div className="px-3 py-2 bg-gray-50 rounded-md text-sm">
                      ${(() => {
                        const item = watchedItems[index]
                        if (!item) return '0.00'
                        const itemTotal = (item.quantity || 0) * (item.unitPrice || 0)
                        const discountAmount = (itemTotal * (item.discount || 0)) / 100
                        const afterDiscount = itemTotal - discountAmount
                        const taxAmount = (afterDiscount * (item.taxRate || 0)) / 100
                        return (afterDiscount + taxAmount).toFixed(2)
                      })()}
                    </div>
                  </div>

                  <div className="col-span-1">
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => removeItem(index)}
                      disabled={fields.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Totals and Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Settings</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="discountType">Discount Type</Label>
                  <select
                    id="discountType"
                    {...register('discountType')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="PERCENTAGE">Percentage</option>
                    <option value="FIXED">Fixed Amount</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="discountValue">
                    Discount {watchedDiscountType === 'PERCENTAGE' ? '%' : '$'}
                  </Label>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    {...register('discountValue', { valueAsNumber: true })}
                    placeholder="0"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="taxRate">Overall Tax Rate (%)</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  {...register('taxRate', { valueAsNumber: true })}
                  placeholder="0"
                />
              </div>

              <div>
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Input
                  {...register('paymentMethod')}
                  placeholder="e.g., Bank Transfer, Credit Card, Check"
                />
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center">
                <Calculator className="h-5 w-5 mr-2" />
                Totals
              </h3>
              
              <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>${totals.subtotal.toFixed(2)}</span>
                </div>
                
                {totals.discountAmount > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Discount:</span>
                    <span>-${totals.discountAmount.toFixed(2)}</span>
                  </div>
                )}
                
                {totals.taxAmount > 0 && (
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>${totals.taxAmount.toFixed(2)}</span>
                  </div>
                )}
                
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>${totals.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Terms and Notes */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="terms">Terms & Conditions</Label>
              <textarea
                id="terms"
                {...register('terms')}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Payment terms, late fees, etc..."
              />
            </div>

            <div>
              <Label htmlFor="notes">Internal Notes</Label>
              <textarea
                id="notes"
                {...register('notes')}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Internal notes (not visible to customer)..."
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : mode === 'create' ? 'Create Invoice' : 'Update Invoice'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for invoice items
const invoiceItemSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  discount: z.number().min(0).max(100).optional().default(0),
  taxRate: z.number().min(0).max(100).optional().default(0)
})

// Validation schema for invoice creation/update
const invoiceSchema = z.object({
  invoiceNumber: z.string().optional(), // Auto-generated if not provided
  customerId: z.string().min(1, 'Customer is required'),
  quotationId: z.string().optional().nullable(),
  status: z.enum(['DRAFT', 'SENT', 'VIEWED', 'PAID', 'OVERDUE', 'CANCELLED']).default('DRAFT'),
  issueDate: z.string().optional(),
  dueDate: z.string().optional(),
  terms: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  items: z.array(invoiceItemSchema).min(1, 'At least one item is required'),
  taxRate: z.number().min(0).max(100).optional().default(0),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).optional().default('PERCENTAGE'),
  discountValue: z.number().min(0).optional().default(0),
  paymentTerms: z.string().optional().nullable(),
  paymentMethod: z.string().optional().nullable()
})

// GET /api/invoices - List invoices with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const customerId = searchParams.get('customerId') || ''
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId || undefined
    }

    if (search) {
      where.OR = [
        { invoiceNumber: { contains: search, mode: 'insensitive' } },
        { customer: { name: { contains: search, mode: 'insensitive' } } },
        { customer: { company: { contains: search, mode: 'insensitive' } } }
      ]
    }

    if (status) {
      where.status = status
    }

    if (customerId) {
      where.customerId = customerId
    }

    // Get invoices with pagination
    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          quotation: {
            select: { id: true, quotationNumber: true, title: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          items: {
            orderBy: { createdAt: 'asc' }
          },
          _count: {
            select: {
              activities: true,
              payments: true
            }
          }
        }
      }),
      prisma.invoice.count({ where })
    ])

    // Calculate totals for each invoice
    const invoicesWithTotals = invoices.map(invoice => {
      const subtotal = invoice.items.reduce((sum, item) => {
        const itemTotal = item.quantity * item.unitPrice
        const discountAmount = (itemTotal * item.discount) / 100
        const afterDiscount = itemTotal - discountAmount
        const taxAmount = (afterDiscount * item.taxRate) / 100
        return sum + afterDiscount + taxAmount
      }, 0)

      let total = subtotal
      if (invoice.discountType === 'PERCENTAGE') {
        total = subtotal - (subtotal * invoice.discountValue) / 100
      } else {
        total = subtotal - invoice.discountValue
      }

      const finalTaxAmount = (total * invoice.taxRate) / 100
      const finalTotal = total + finalTaxAmount

      return {
        ...invoice,
        subtotal: Math.round(subtotal * 100) / 100,
        total: Math.round(finalTotal * 100) / 100
      }
    })

    return NextResponse.json({
      invoices: invoicesWithTotals,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching invoices:', error)
    return NextResponse.json(
      { error: 'Failed to fetch invoices' },
      { status: 500 }
    )
  }
}

// POST /api/invoices - Create new invoice
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = invoiceSchema.parse(body)

    // Generate invoice number if not provided
    let invoiceNumber = validatedData.invoiceNumber
    if (!invoiceNumber) {
      const currentYear = new Date().getFullYear()
      const lastInvoice = await prisma.invoice.findFirst({
        where: {
          companyId: session.user.companyId || undefined,
          invoiceNumber: {
            startsWith: `INV-${currentYear}-`
          }
        },
        orderBy: { invoiceNumber: 'desc' }
      })

      let nextNumber = 1
      if (lastInvoice) {
        const lastNumber = parseInt(lastInvoice.invoiceNumber.split('-')[2])
        nextNumber = lastNumber + 1
      }

      invoiceNumber = `INV-${currentYear}-${nextNumber.toString().padStart(4, '0')}`
    }

    // Prepare invoice data
    const invoiceData: any = {
      ...validatedData,
      invoiceNumber,
      companyId: session.user.companyId!,
      createdById: session.user.id
    }

    if (validatedData.issueDate) {
      invoiceData.issueDate = new Date(validatedData.issueDate)
    } else {
      invoiceData.issueDate = new Date()
    }

    if (validatedData.dueDate) {
      invoiceData.dueDate = new Date(validatedData.dueDate)
    } else {
      // Default to 30 days from issue date
      const dueDate = new Date(invoiceData.issueDate)
      dueDate.setDate(dueDate.getDate() + 30)
      invoiceData.dueDate = dueDate
    }

    // Create invoice with items in a transaction
    const invoice = await prisma.$transaction(async (tx) => {
      const newInvoice = await tx.invoice.create({
        data: {
          ...invoiceData,
          items: {
            create: validatedData.items.map(item => ({
              ...item,
              companyId: session.user.companyId!
            }))
          }
        },
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          quotation: {
            select: { id: true, quotationNumber: true, title: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          items: {
            orderBy: { createdAt: 'asc' }
          }
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'NOTE',
          title: 'Invoice Created',
          description: `Invoice "${invoiceNumber}" was created`,
          invoiceId: newInvoice.id,
          customerId: newInvoice.customerId,
          quotationId: newInvoice.quotationId,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })

      // If created from quotation, update quotation status
      if (newInvoice.quotationId) {
        await tx.quotation.update({
          where: { id: newInvoice.quotationId },
          data: { status: 'ACCEPTED' }
        })

        await tx.activity.create({
          data: {
            type: 'STATUS_CHANGE',
            title: 'Quotation Converted to Invoice',
            description: `Quotation was converted to invoice "${invoiceNumber}"`,
            quotationId: newInvoice.quotationId,
            invoiceId: newInvoice.id,
            customerId: newInvoice.customerId,
            companyId: session.user.companyId!,
            createdById: session.user.id
          }
        })
      }

      return newInvoice
    })

    return NextResponse.json(invoice, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating invoice:', error)
    return NextResponse.json(
      { error: 'Failed to create invoice' },
      { status: 500 }
    )
  }
}

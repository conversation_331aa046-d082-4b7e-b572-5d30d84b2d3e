const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('Setting up test data...')

    // Check if test user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingUser) {
      console.log('Test user already exists')
      console.log('Email: <EMAIL>')
      console.log('Password: password123')
      return
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('password123', 12)

    // Create test company with owner
    const company = await prisma.company.create({
      data: {
        name: 'Test Company',
        email: '<EMAIL>',
        phone: '******-0123',
        address: '123 Test Street',
        website: 'https://test.com',
        industry: 'Technology',
        size: 'SMALL',
        status: 'ACTIVE',
        owner: {
          create: {
            email: '<EMAIL>',
            password: hashedPassword,
            name: 'Test Admin',
            role: 'ADMIN',
            status: 'ACTIVE',
            emailVerified: new Date(),
            loginCount: 0
          }
        }
      },
      include: {
        owner: true
      }
    })

    console.log('Created company:', company.name)
    const user = company.owner

    console.log('Created user:', user.name)

    // Create some test customers
    const customer1 = await prisma.customer.create({
      data: {
        name: 'John Doe',
        email: '<EMAIL>',
        company: 'Example Corp',
        phone: '******-0001',
        address: '456 Example St',
        city: 'Example City',
        state: 'Example State',
        country: 'USA',
        postalCode: '54321',
        status: 'ACTIVE',
        companyId: company.id,
        createdById: user.id
      }
    })

    const customer2 = await prisma.customer.create({
      data: {
        name: 'Jane Smith',
        email: '<EMAIL>',
        company: 'Demo Inc',
        phone: '******-0002',
        address: '789 Demo Ave',
        city: 'Demo City',
        state: 'Demo State',
        country: 'USA',
        postalCode: '67890',
        status: 'ACTIVE',
        companyId: company.id,
        createdById: user.id
      }
    })

    console.log('Created customers:', customer1.name, customer2.name)

    // Create some test quotations
    const quotation1 = await prisma.quotation.create({
      data: {
        quotationNumber: 'QUO-2024-0001',
        title: 'Website Development',
        description: 'Complete website development project',
        customerId: customer1.id,
        status: 'DRAFT',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        subtotal: 5000,
        taxRate: 10,
        taxAmount: 500,
        total: 5500,
        companyId: company.id,
        createdById: user.id,
        items: {
          create: [
            {
              name: 'Frontend Development',
              description: 'React frontend development',
              quantity: 1,
              unitPrice: 3000,
              total: 3000
            },
            {
              name: 'Backend Development',
              description: 'Node.js backend development',
              quantity: 1,
              unitPrice: 2000,
              total: 2000
            }
          ]
        }
      }
    })

    const quotation2 = await prisma.quotation.create({
      data: {
        quotationNumber: 'QUO-2024-0002',
        title: 'Mobile App Development',
        description: 'iOS and Android mobile app',
        customerId: customer2.id,
        status: 'ACCEPTED',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        subtotal: 8000,
        taxRate: 10,
        taxAmount: 800,
        total: 8800,
        companyId: company.id,
        createdById: user.id,
        items: {
          create: [
            {
              name: 'iOS App Development',
              description: 'Native iOS application',
              quantity: 1,
              unitPrice: 4000,
              total: 4000
            },
            {
              name: 'Android App Development',
              description: 'Native Android application',
              quantity: 1,
              unitPrice: 4000,
              total: 4000
            }
          ]
        }
      }
    })

    console.log('Created quotations:', quotation1.quotationNumber, quotation2.quotationNumber)

    // Create a test invoice
    const invoice1 = await prisma.invoice.create({
      data: {
        invoiceNumber: 'INV-2024-0001',
        title: 'Website Development Invoice',
        customerId: customer1.id,
        quotationId: quotation1.id,
        status: 'DRAFT',
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        subtotal: 5000,
        taxRate: 10,
        taxAmount: 500,
        total: 5500,
        companyId: company.id,
        createdById: user.id,
        items: {
          create: [
            {
              name: 'Frontend Development',
              description: 'React frontend development',
              quantity: 1,
              unitPrice: 3000,
              total: 3000
            },
            {
              name: 'Backend Development',
              description: 'Node.js backend development',
              quantity: 1,
              unitPrice: 2000,
              total: 2000
            }
          ]
        }
      }
    })

    console.log('Created invoice:', invoice1.invoiceNumber)

    console.log('\n✅ Test data setup complete!')
    console.log('\nLogin credentials:')
    console.log('Email: <EMAIL>')
    console.log('Password: password123')
    console.log('\nYou can now sign in and test the application.')

  } catch (error) {
    console.error('Error setting up test data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()

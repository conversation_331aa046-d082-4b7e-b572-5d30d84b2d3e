"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/customers/page",{

/***/ "(app-pages-browser)/./app/dashboard/customers/page.tsx":
/*!******************************************!*\
  !*** ./app/dashboard/customers/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CustomersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_customers_customer_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/customers/customer-form */ \"(app-pages-browser)/./components/customers/customer-form.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Trash2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CustomersPage() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCustomer, setEditingCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        active: 0,\n        prospects: 0,\n        inactive: 0\n    });\n    const fetchCustomers = async ()=>{\n        try {\n            const response = await fetch(\"/api/customers\");\n            if (!response.ok) throw new Error(\"Failed to fetch customers\");\n            const data = await response.json();\n            setCustomers(data.customers);\n            // Calculate stats\n            const total = data.customers.length;\n            const active = data.customers.filter((c)=>c.status === \"ACTIVE\").length;\n            const prospects = data.customers.filter((c)=>c.status === \"PROSPECT\").length;\n            const inactive = data.customers.filter((c)=>c.status === \"INACTIVE\").length;\n            setStats({\n                total,\n                active,\n                prospects,\n                inactive\n            });\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to load customers\");\n            console.error(\"Error fetching customers:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCustomers();\n    }, []);\n    const handleDelete = async (customer)=>{\n        if (!confirm(\"Are you sure you want to delete \".concat(customer.name, \"?\"))) return;\n        try {\n            const response = await fetch(\"/api/customers/\".concat(customer.id), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to delete customer\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Customer deleted successfully\");\n            fetchCustomers();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(error instanceof Error ? error.message : \"Failed to delete customer\");\n        }\n    };\n    const handleEdit = (customer)=>{\n        setEditingCustomer(customer);\n        setShowForm(true);\n    };\n    const handleFormClose = ()=>{\n        setShowForm(false);\n        setEditingCustomer(null);\n    };\n    const getStatusBadge = (status)=>{\n        if (!status) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n            lineNumber: 129,\n            columnNumber: 25\n        }, this);\n        switch(status){\n            case \"ACTIVE\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"success\",\n                    children: \"Active\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, this);\n            case \"INACTIVE\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Inactive\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 16\n                }, this);\n            case \"PROSPECT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"warning\",\n                    children: \"Prospect\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const columns = [\n        {\n            accessorKey: \"name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const customer = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-blue-600 font-medium text-sm\",\n                                children: customer.name.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium\",\n                                    children: customer.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                customer.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: customer.company\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const customer = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        customer.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: customer.email\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, this),\n                        customer.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: customer.phone\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                return getStatusBadge(row.getValue(\"status\"));\n            }\n        },\n        {\n            accessorKey: \"industry\",\n            header: \"Industry\",\n            cell: (param)=>{\n                let { row } = param;\n                const industry = row.getValue(\"industry\");\n                return industry ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-3 w-3 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: industry\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-400 text-sm\",\n                    children: \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"_count\",\n            header: \"Activity\",\n            cell: (param)=>{\n                let { row } = param;\n                const count = row.getValue(\"_count\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-3 w-3 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: count.leads\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-3 w-3 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: count.quotations\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"tags\",\n            header: \"Tags\",\n            cell: (param)=>{\n                let { row } = param;\n                const tags = row.getValue(\"tags\");\n                if (!tags || !Array.isArray(tags) || tags.length === 0) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 text-sm\",\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 18\n                    }, this);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-1\",\n                    children: [\n                        tags.slice(0, 2).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                variant: \"outline\",\n                                className: \"text-xs\",\n                                children: tag\n                            }, tag, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this)),\n                        tags.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                            variant: \"outline\",\n                            className: \"text-xs\",\n                            children: [\n                                \"+\",\n                                tags.length - 2\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const customer = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                            align: \"end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuLabel, {\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: \"/dashboard/customers/\".concat(customer.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"View Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                    onClick: ()=>handleEdit(customer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Edit\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                    onClick: ()=>handleDelete(customer),\n                                    className: \"text-red-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Delete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Customers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage your customer relationships\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        className: \"flex items-center space-x-2\",\n                        onClick: ()=>setShowForm(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Add Customer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Customers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"All customers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.active\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Active customers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Prospects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.prospects\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Potential customers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Inactive\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Trash2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.inactive\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Inactive customers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"Customer Directory\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n                            columns: columns,\n                            data: customers,\n                            searchPlaceholder: \"Search customers...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customers_customer_form__WEBPACK_IMPORTED_MODULE_7__.CustomerForm, {\n                isOpen: showForm,\n                onClose: handleFormClose,\n                onSuccess: fetchCustomers,\n                customer: editingCustomer,\n                mode: editingCustomer ? \"edit\" : \"create\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\customers\\\\page.tsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomersPage, \"M1mzbhQhlakysIheqQVKv36P2Ig=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = CustomersPage;\nvar _c;\n$RefreshReg$(_c, \"CustomersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/customers/page.tsx\n"));

/***/ })

});
'use client'

import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Users,
  FileText,
  Receipt,
  DollarSign,
  UserPlus,
  Activity,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  Plus
} from 'lucide-react'

export default function DashboardPage() {
  const { data: session } = useSession()

  // Mock data for demonstration
  const stats = [
    {
      title: 'Total Customers',
      value: '2,345',
      change: '+20.1%',
      changeType: 'increase' as const,
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: 'Active Leads',
      value: '156',
      change: '+15.3%',
      changeType: 'increase' as const,
      icon: UserPlus,
      color: 'text-green-600'
    },
    {
      title: 'Quotations',
      value: '89',
      change: '+8.2%',
      changeType: 'increase' as const,
      icon: FileText,
      color: 'text-yellow-600'
    },
    {
      title: 'Revenue',
      value: '$45,231',
      change: '+12.5%',
      changeType: 'increase' as const,
      icon: DollarSign,
      color: 'text-purple-600'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'customer',
      message: 'New customer "Acme Corp" was added',
      time: '2 minutes ago',
      icon: Users,
      status: 'success'
    },
    {
      id: 2,
      type: 'quotation',
      message: 'Quotation #Q-2024-001 was sent to client',
      time: '1 hour ago',
      icon: FileText,
      status: 'info'
    },
    {
      id: 3,
      type: 'payment',
      message: 'Payment of $5,000 received from TechStart Inc',
      time: '3 hours ago',
      icon: Receipt,
      status: 'success'
    },
    {
      id: 4,
      type: 'lead',
      message: 'New lead "Digital Solutions" was qualified',
      time: '5 hours ago',
      icon: UserPlus,
      status: 'warning'
    }
  ]

  const quickActions = [
    {
      title: 'Add Customer',
      description: 'Create a new customer profile',
      icon: Users,
      href: '/dashboard/customers/new'
    },
    {
      title: 'Create Quotation',
      description: 'Generate a new quotation',
      icon: FileText,
      href: '/dashboard/quotations/new'
    },
    {
      title: 'Add Lead',
      description: 'Track a new business lead',
      icon: UserPlus,
      href: '/dashboard/leads/new'
    },
    {
      title: 'View Reports',
      description: 'Analyze your business data',
      icon: TrendingUp,
      href: '/dashboard/reports'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome back, {session?.user?.name || session?.user?.email || 'User'}!
        </h1>
        <p className="text-blue-100">
          Here's what's happening with your business today.
        </p>
        <div className="mt-4 flex items-center space-x-4 text-sm">
          <span className="flex items-center">
            <Badge variant="secondary" className="mr-2">
              {session?.user?.role || 'USER'}
            </Badge>
            Role
          </span>
          {session?.user?.company?.name && (
            <span className="flex items-center">
              <Badge variant="outline" className="mr-2 text-white border-white">
                {session.user.company.name}
              </Badge>
              Company
            </span>
          )}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.title} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <stat.icon className={`h-5 w-5 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              <div className="flex items-center mt-1">
                {stat.changeType === 'increase' ? (
                  <ArrowUpRight className="h-4 w-4 text-green-600 mr-1" />
                ) : (
                  <ArrowDownRight className="h-4 w-4 text-red-600 mr-1" />
                )}
                <span className={`text-sm font-medium ${
                  stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change}
                </span>
                <span className="text-sm text-gray-500 ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Plus className="h-5 w-5 mr-2" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action) => (
                <Button
                  key={action.title}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 hover:bg-gray-50"
                  asChild
                >
                  <a href={action.href}>
                    <action.icon className="h-6 w-6 text-gray-600" />
                    <div className="text-center">
                      <div className="font-medium text-sm">{action.title}</div>
                      <div className="text-xs text-gray-500">{action.description}</div>
                    </div>
                  </a>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`p-2 rounded-full ${
                    activity.status === 'success' ? 'bg-green-100' :
                    activity.status === 'warning' ? 'bg-yellow-100' :
                    'bg-blue-100'
                  }`}>
                    <activity.icon className={`h-4 w-4 ${
                      activity.status === 'success' ? 'text-green-600' :
                      activity.status === 'warning' ? 'text-yellow-600' :
                      'text-blue-600'
                    }`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

    </div>
  )
}

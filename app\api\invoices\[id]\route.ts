import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for invoice items
const invoiceItemSchema = z.object({
  id: z.string().optional(),
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  discount: z.number().min(0).max(100).optional().default(0),
  taxRate: z.number().min(0).max(100).optional().default(0)
})

// Validation schema for invoice update
const invoiceUpdateSchema = z.object({
  customerId: z.string().min(1, 'Customer is required').optional(),
  quotationId: z.string().optional().nullable(),
  status: z.enum(['DRAFT', 'SENT', 'VIEWED', 'PAID', 'OVERDUE', 'CANCELLED']).optional(),
  issueDate: z.string().optional(),
  dueDate: z.string().optional(),
  terms: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  items: z.array(invoiceItemSchema).min(1, 'At least one item is required').optional(),
  taxRate: z.number().min(0).max(100).optional(),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).optional(),
  discountValue: z.number().min(0).optional(),
  paymentTerms: z.string().optional().nullable(),
  paymentMethod: z.string().optional().nullable()
})

// GET /api/invoices/[id] - Get single invoice
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        customer: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            company: true, 
            phone: true, 
            address: true, 
            city: true, 
            state: true, 
            country: true, 
            postalCode: true 
          }
        },
        quotation: {
          select: { id: true, quotationNumber: true, title: true }
        },
        createdBy: {
          select: { name: true, email: true }
        },
        items: {
          orderBy: { createdAt: 'asc' }
        },
        payments: {
          orderBy: { createdAt: 'desc' },
          include: {
            createdBy: { select: { name: true } }
          }
        },
        activities: {
          orderBy: { createdAt: 'desc' },
          take: 10,
          include: {
            createdBy: { select: { name: true } }
          }
        },
        _count: {
          select: {
            activities: true,
            payments: true
          }
        }
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Calculate totals
    const subtotal = invoice.items.reduce((sum, item) => {
      const itemTotal = item.quantity * item.unitPrice
      const discountAmount = (itemTotal * item.discount) / 100
      const afterDiscount = itemTotal - discountAmount
      const taxAmount = (afterDiscount * item.taxRate) / 100
      return sum + afterDiscount + taxAmount
    }, 0)

    let total = subtotal
    if (invoice.discountType === 'PERCENTAGE') {
      total = subtotal - (subtotal * invoice.discountValue) / 100
    } else {
      total = subtotal - invoice.discountValue
    }

    const finalTaxAmount = (total * invoice.taxRate) / 100
    const finalTotal = total + finalTaxAmount

    // Calculate payment totals
    const totalPaid = invoice.payments.reduce((sum, payment) => sum + payment.amount, 0)
    const amountDue = finalTotal - totalPaid

    const invoiceWithTotals = {
      ...invoice,
      subtotal: Math.round(subtotal * 100) / 100,
      total: Math.round(finalTotal * 100) / 100,
      taxAmount: Math.round(finalTaxAmount * 100) / 100,
      discountAmount: invoice.discountType === 'PERCENTAGE' 
        ? Math.round((subtotal * invoice.discountValue / 100) * 100) / 100
        : invoice.discountValue,
      totalPaid: Math.round(totalPaid * 100) / 100,
      amountDue: Math.round(amountDue * 100) / 100
    }

    return NextResponse.json(invoiceWithTotals)
  } catch (error) {
    console.error('Error fetching invoice:', error)
    return NextResponse.json(
      { error: 'Failed to fetch invoice' },
      { status: 500 }
    )
  }
}

// PUT /api/invoices/[id] - Update invoice
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = invoiceUpdateSchema.parse(body)

    // Check if invoice exists and belongs to user's company
    const existingInvoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        items: true
      }
    })

    if (!existingInvoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Check if invoice can be edited (not paid or cancelled)
    if (existingInvoice.status === 'PAID' || existingInvoice.status === 'CANCELLED') {
      return NextResponse.json(
        { error: 'Cannot edit paid or cancelled invoices' },
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData: any = { ...validatedData }

    if (validatedData.issueDate) {
      updateData.issueDate = new Date(validatedData.issueDate)
    }

    if (validatedData.dueDate) {
      updateData.dueDate = new Date(validatedData.dueDate)
    }

    // Update invoice with items in a transaction
    const invoice = await prisma.$transaction(async (tx) => {
      // Update invoice
      const updatedInvoice = await tx.invoice.update({
        where: { id: params.id },
        data: updateData,
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          quotation: {
            select: { id: true, quotationNumber: true, title: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          items: {
            orderBy: { createdAt: 'asc' }
          }
        }
      })

      // Update items if provided
      if (validatedData.items) {
        // Delete existing items
        await tx.invoiceItem.deleteMany({
          where: { invoiceId: params.id }
        })

        // Create new items
        await tx.invoiceItem.createMany({
          data: validatedData.items.map(item => ({
            ...item,
            invoiceId: params.id,
            companyId: session.user.companyId!
          }))
        })

        // Fetch updated invoice with new items
        const finalInvoice = await tx.invoice.findUnique({
          where: { id: params.id },
          include: {
            customer: {
              select: { id: true, name: true, email: true, company: true }
            },
            quotation: {
              select: { id: true, quotationNumber: true, title: true }
            },
            createdBy: {
              select: { name: true, email: true }
            },
            items: {
              orderBy: { createdAt: 'asc' }
            }
          }
        })

        // Log activity if status changed
        if (validatedData.status && validatedData.status !== existingInvoice.status) {
          await tx.activity.create({
            data: {
              type: 'STATUS_CHANGE',
              title: 'Invoice Status Updated',
              description: `Invoice status changed from ${existingInvoice.status} to ${validatedData.status}`,
              invoiceId: params.id,
              customerId: updatedInvoice.customerId,
              quotationId: updatedInvoice.quotationId,
              companyId: session.user.companyId!,
              createdById: session.user.id
            }
          })
        }

        return finalInvoice
      }

      return updatedInvoice
    })

    return NextResponse.json(invoice)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating invoice:', error)
    return NextResponse.json(
      { error: 'Failed to update invoice' },
      { status: 500 }
    )
  }
}

// DELETE /api/invoices/[id] - Delete invoice
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if invoice exists and belongs to user's company
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        _count: {
          select: {
            payments: true
          }
        }
      }
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Check if invoice has payments
    if (invoice._count.payments > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete invoice with existing payments',
          details: invoice._count
        },
        { status: 400 }
      )
    }

    // Check if invoice is paid
    if (invoice.status === 'PAID') {
      return NextResponse.json(
        { error: 'Cannot delete paid invoices' },
        { status: 400 }
      )
    }

    await prisma.invoice.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Invoice deleted successfully' })
  } catch (error) {
    console.error('Error deleting invoice:', error)
    return NextResponse.json(
      { error: 'Failed to delete invoice' },
      { status: 500 }
    )
  }
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leads/page",{

/***/ "(app-pages-browser)/./app/dashboard/leads/page.tsx":
/*!**************************************!*\
  !*** ./app/dashboard/leads/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeadsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_leads_lead_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/leads/lead-form */ \"(app-pages-browser)/./components/leads/lead-form.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Edit,Eye,Mail,MoreHorizontal,Phone,Plus,Target,Trash2,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction LeadsPage() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [leads, setLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingLead, setEditingLead] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        new: 0,\n        qualified: 0,\n        closedWon: 0,\n        totalValue: 0\n    });\n    const fetchLeads = async ()=>{\n        try {\n            const response = await fetch(\"/api/leads\");\n            if (!response.ok) throw new Error(\"Failed to fetch leads\");\n            const data = await response.json();\n            setLeads(data.leads);\n            // Calculate stats\n            const total = data.leads.length;\n            const newLeads = data.leads.filter((l)=>l.status === \"NEW\").length;\n            const qualified = data.leads.filter((l)=>l.status === \"QUALIFIED\").length;\n            const closedWon = data.leads.filter((l)=>l.status === \"CLOSED_WON\").length;\n            const totalValue = data.leads.reduce((sum, l)=>sum + (l.budget || 0), 0);\n            setStats({\n                total,\n                new: newLeads,\n                qualified,\n                closedWon,\n                totalValue\n            });\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to load leads\");\n            console.error(\"Error fetching leads:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLeads();\n    }, []);\n    const handleDelete = async (lead)=>{\n        if (!confirm('Are you sure you want to delete \"'.concat(lead.firstName, \" \").concat(lead.lastName, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/leads/\".concat(lead.id), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to delete lead\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Lead deleted successfully\");\n            fetchLeads();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(error instanceof Error ? error.message : \"Failed to delete lead\");\n        }\n    };\n    const handleEdit = (lead)=>{\n        setEditingLead(lead);\n        setShowForm(true);\n    };\n    const handleFormClose = ()=>{\n        setShowForm(false);\n        setEditingLead(null);\n    };\n    const getStatusBadge = (status)=>{\n        if (!status) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n            lineNumber: 159,\n            columnNumber: 25\n        }, this);\n        switch(status){\n            case \"NEW\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"New\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 16\n                }, this);\n            case \"CONTACTED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"info\",\n                    children: \"Contacted\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 16\n                }, this);\n            case \"QUALIFIED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"success\",\n                    children: \"Qualified\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 16\n                }, this);\n            case \"PROPOSAL\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"warning\",\n                    children: \"Proposal\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 16\n                }, this);\n            case \"NEGOTIATION\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"warning\",\n                    children: \"Negotiation\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 16\n                }, this);\n            case \"CLOSED_WON\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"success\",\n                    children: \"Closed Won\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 16\n                }, this);\n            case \"CLOSED_LOST\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: \"Closed Lost\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getPriorityBadge = (priority)=>{\n        if (!priority) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            className: \"text-xs\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 27\n        }, this);\n        switch(priority){\n            case \"LOW\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    className: \"text-xs\",\n                    children: \"Low\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 16\n                }, this);\n            case \"MEDIUM\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"info\",\n                    className: \"text-xs\",\n                    children: \"Medium\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 16\n                }, this);\n            case \"HIGH\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"warning\",\n                    className: \"text-xs\",\n                    children: \"High\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 16\n                }, this);\n            case \"URGENT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    className: \"text-xs\",\n                    children: \"Urgent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    className: \"text-xs\",\n                    children: priority\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const columns = [\n        {\n            accessorKey: \"firstName\",\n            header: \"Lead\",\n            cell: (param)=>{\n                let { row } = param;\n                const lead = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        lead.firstName,\n                                        \" \",\n                                        lead.lastName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this),\n                                lead.companyName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: lead.companyName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 17\n                                }, this),\n                                lead.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: lead.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const lead = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: lead.email\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        lead.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: lead.phone\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                return getStatusBadge(row.getValue(\"status\"));\n            }\n        },\n        {\n            accessorKey: \"priority\",\n            header: \"Priority\",\n            cell: (param)=>{\n                let { row } = param;\n                return getPriorityBadge(row.getValue(\"priority\"));\n            }\n        },\n        {\n            accessorKey: \"budget\",\n            header: \"Budget\",\n            cell: (param)=>{\n                let { row } = param;\n                const budget = row.getValue(\"budget\");\n                return budget ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-3 w-3 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: [\n                                \"$\",\n                                budget.toLocaleString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-400 text-sm\",\n                    children: \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"score\",\n            header: \"Score\",\n            cell: (param)=>{\n                let { row } = param;\n                const score = row.getValue(\"score\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-3 w-3 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: [\n                                score,\n                                \"/100\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"source\",\n            header: \"Source\",\n            cell: (param)=>{\n                let { row } = param;\n                const source = row.getValue(\"source\");\n                return source ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"outline\",\n                    className: \"text-xs\",\n                    children: source\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-400 text-sm\",\n                    children: \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const lead = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                            align: \"end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuLabel, {\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: \"/dashboard/leads/\".concat(lead.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"View Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                    onClick: ()=>handleEdit(lead),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Edit\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                    onClick: ()=>handleDelete(lead),\n                                    className: \"text-red-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Delete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Leads\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Track and convert your business leads\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                    href: \"/dashboard/leads/pipeline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Pipeline View\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                className: \"flex items-center space-x-2\",\n                                onClick: ()=>setShowForm(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Add Lead\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Leads\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"All leads\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"New\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.new\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"New leads\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Qualified\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.qualified\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Qualified leads\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Closed Won\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.closedWon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Successful conversions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Pipeline Value\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Edit_Eye_Mail_MoreHorizontal_Phone_Plus_Target_Trash2_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: [\n                                            \"$\",\n                                            stats.totalValue.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Total estimated value\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: \"Lead Pipeline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n                            columns: columns,\n                            data: leads,\n                            searchPlaceholder: \"Search leads...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                lineNumber: 413,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leads_lead_form__WEBPACK_IMPORTED_MODULE_7__.LeadForm, {\n                isOpen: showForm,\n                onClose: handleFormClose,\n                onSuccess: fetchLeads,\n                lead: editingLead,\n                mode: editingLead ? \"edit\" : \"create\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\page.tsx\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, this);\n}\n_s(LeadsPage, \"RqSATtBKxivSuRCBue9bDFK1zgQ=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = LeadsPage;\nvar _c;\n$RefreshReg$(_c, \"LeadsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/leads/page.tsx\n"));

/***/ })

});
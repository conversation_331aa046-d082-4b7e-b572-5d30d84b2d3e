'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { ColumnDef } from '@tanstack/react-table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { InvoiceForm } from '@/components/invoices/invoice-form'
import { InvoiceAnalytics } from '@/components/invoices/invoice-analytics'
import { PaymentModal } from '@/components/invoices/payment-modal'
import { 
  FileText, 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  Send,
  Download,
  Copy,
  DollarSign,
  Calendar,
  User,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  CreditCard,
  BarChart3
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface Invoice {
  id: string
  invoiceNumber: string
  status: 'DRAFT' | 'SENT' | 'VIEWED' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  issueDate: string
  dueDate: string
  subtotal: number
  total: number
  customer: {
    id: string
    name: string
    email: string | null
    companyName: string | null
  }
  quotation: {
    id: string
    quotationNumber: string
    title: string
  } | null
  createdBy: {
    name: string | null
    email: string | null
  }
  items: any[]
  _count: {
    activities: number
    payments: number
  }
}

export default function InvoicesPage() {
  const { data: session } = useSession()
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<Invoice | null>(null)
  const [showAnalytics, setShowAnalytics] = useState(false)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [paymentInvoice, setPaymentInvoice] = useState<Invoice | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    draft: 0,
    sent: 0,
    paid: 0,
    overdue: 0,
    totalValue: 0,
    totalPaid: 0
  })

  const fetchInvoices = useCallback(async () => {
    try {
      const response = await fetch('/api/invoices')
      if (!response.ok) throw new Error('Failed to fetch invoices')

      const data = await response.json()
      setInvoices(data.invoices)

      // Calculate stats
      const total = data.invoices.length
      const draft = data.invoices.filter((i: Invoice) => i.status === 'DRAFT').length
      const sent = data.invoices.filter((i: Invoice) => i.status === 'SENT').length
      const paid = data.invoices.filter((i: Invoice) => i.status === 'PAID').length
      const overdue = data.invoices.filter((i: Invoice) => i.status === 'OVERDUE').length
      const totalValue = data.invoices.reduce((sum: number, i: Invoice) => sum + (i.total || 0), 0)
      const totalPaid = data.invoices
        .filter((i: Invoice) => i.status === 'PAID')
        .reduce((sum: number, i: Invoice) => sum + (i.total || 0), 0)

      setStats({ total, draft, sent, paid, overdue, totalValue, totalPaid })
    } catch (error) {
      toast.error('Failed to load invoices')
      console.error('Error fetching invoices:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchInvoices()
  }, [fetchInvoices])

  const handleDelete = useCallback(async (invoice: Invoice) => {
    if (!confirm(`Are you sure you want to delete invoice "${invoice.invoiceNumber}"?`)) return

    try {
      const response = await fetch(`/api/invoices/${invoice.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete invoice')
      }

      toast.success('Invoice deleted successfully')
      fetchInvoices()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete invoice')
    }
  }, [fetchInvoices])

  const handleEdit = useCallback((invoice: Invoice) => {
    setEditingInvoice(invoice)
    setShowForm(true)
  }, [])

  const handleFormClose = () => {
    setShowForm(false)
    setEditingInvoice(null)
  }

  const handleDownloadPDF = useCallback(async (invoiceId: string) => {
    try {
      const response = await fetch(`/api/invoices/${invoiceId}/pdf`)
      if (!response.ok) {
        throw new Error('Failed to generate PDF')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `invoice-${invoiceId}.html`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast.success('PDF downloaded successfully')
    } catch (error) {
      toast.error('Failed to download PDF')
      console.error('Error downloading PDF:', error)
    }
  }, [])

  const handleRecordPayment = useCallback((invoice: Invoice) => {
    setPaymentInvoice(invoice)
    setShowPaymentModal(true)
  }, [])

  const getStatusBadge = (status: string | null | undefined) => {
    if (!status) return <Badge variant="secondary">Unknown</Badge>
    
    switch (status) {
      case 'DRAFT':
        return <Badge variant="secondary">Draft</Badge>
      case 'SENT':
        return <Badge variant="info">Sent</Badge>
      case 'VIEWED':
        return <Badge variant="warning">Viewed</Badge>
      case 'PAID':
        return <Badge variant="success">Paid</Badge>
      case 'OVERDUE':
        return <Badge variant="destructive">Overdue</Badge>
      case 'CANCELLED':
        return <Badge variant="secondary">Cancelled</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const columns: ColumnDef<Invoice>[] = [
    {
      accessorKey: 'invoiceNumber',
      header: 'Invoice',
      cell: ({ row }) => {
        const invoice = row.original
        return (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <FileText className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <div className="font-medium">{invoice.invoiceNumber}</div>
              <div className="text-sm text-gray-500">
                {invoice.quotation ? `From ${invoice.quotation.quotationNumber}` : 'Direct Invoice'}
              </div>
            </div>
          </div>
        )
      }
    },
    {
      accessorKey: 'customer',
      header: 'Customer',
      cell: ({ row }) => {
        const customer = row.original.customer
        return (
          <div>
            <div className="font-medium">{customer.name}</div>
            {customer.companyName && (
              <div className="text-sm text-gray-500">{customer.companyName}</div>
            )}
          </div>
        )
      }
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => getStatusBadge(row.getValue('status'))
    },
    {
      accessorKey: 'total',
      header: 'Amount',
      cell: ({ row }) => {
        const total = row.getValue('total') as number
        return (
          <div className="flex items-center space-x-2">
            <DollarSign className="h-3 w-3 text-green-600" />
            <span className="font-medium">${total.toLocaleString()}</span>
          </div>
        )
      }
    },
    {
      accessorKey: 'issueDate',
      header: 'Issue Date',
      cell: ({ row }) => {
        const date = row.getValue('issueDate') as string
        return (
          <div className="flex items-center space-x-2">
            <Calendar className="h-3 w-3 text-gray-400" />
            <span className="text-sm">{new Date(date).toLocaleDateString()}</span>
          </div>
        )
      }
    },
    {
      accessorKey: 'dueDate',
      header: 'Due Date',
      cell: ({ row }) => {
        const date = row.getValue('dueDate') as string
        const dueDate = new Date(date)
        const today = new Date()
        const isOverdue = dueDate < today && row.original.status !== 'PAID'
        
        return (
          <div className="flex items-center space-x-2">
            <Calendar className={`h-3 w-3 ${isOverdue ? 'text-red-400' : 'text-gray-400'}`} />
            <span className={`text-sm ${isOverdue ? 'text-red-600' : ''}`}>
              {dueDate.toLocaleDateString()}
            </span>
          </div>
        )
      }
    },
    {
      accessorKey: 'createdBy',
      header: 'Created By',
      cell: ({ row }) => {
        const createdBy = row.original.createdBy
        return (
          <div className="flex items-center space-x-2">
            <User className="h-3 w-3 text-gray-400" />
            <span className="text-sm">{createdBy.name || 'Unknown'}</span>
          </div>
        )
      }
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const invoice = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/invoices/${invoice.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEdit(invoice)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Send className="mr-2 h-4 w-4" />
                Send to Customer
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDownloadPDF(invoice.id)}>
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleRecordPayment(invoice)}>
                <CreditCard className="mr-2 h-4 w-4" />
                Record Payment
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleDelete(invoice)}
                className="text-red-600"
                disabled={invoice.status === 'PAID' || invoice._count.payments > 0}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      }
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Invoices</h1>
          <p className="text-gray-600 mt-1">Create and manage your invoices</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => setShowAnalytics(!showAnalytics)}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Invoice
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">All invoices</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Draft</CardTitle>
            <Clock className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.draft}</div>
            <p className="text-xs text-muted-foreground">Draft invoices</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sent</CardTitle>
            <Send className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.sent}</div>
            <p className="text-xs text-muted-foreground">Sent to customers</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.paid}</div>
            <p className="text-xs text-muted-foreground">Paid invoices</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.overdue}</div>
            <p className="text-xs text-muted-foreground">Overdue invoices</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total invoice value</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Paid</CardTitle>
            <CreditCard className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.totalPaid.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total payments received</p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics */}
      {showAnalytics && (
        <InvoiceAnalytics />
      )}

      {/* Invoices Table */}
      <Card>
        <CardHeader>
          <CardTitle>Invoice Management</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={invoices}
              searchPlaceholder="Search invoices..."
            />
          )}
        </CardContent>
      </Card>

      {/* Invoice Form Modal */}
      <InvoiceForm
        isOpen={showForm}
        onClose={handleFormClose}
        onSuccess={fetchInvoices}
        invoice={editingInvoice}
        mode={editingInvoice ? 'edit' : 'create'}
      />

      {/* Payment Modal */}
      <PaymentModal
        open={showPaymentModal}
        invoice={paymentInvoice}
        onClose={() => {
          setShowPaymentModal(false)
          setPaymentInvoice(null)
        }}
        onSuccess={() => {
          setShowPaymentModal(false)
          setPaymentInvoice(null)
          fetchInvoices()
        }}
      />
    </div>
  )
}

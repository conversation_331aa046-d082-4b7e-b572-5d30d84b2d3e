generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// ENTERPRISE BUSINESS MANAGEMENT SYSTEM SCHEMA
// Complete CRM, Sales, Invoicing, Contracts, and Project Management
// ============================================================================

// ============================================================================
// ENUMS
// ============================================================================

enum UserRole {
  SUPER_ADMIN
  ADMIN
  MANAGER
  SALES
  ACCOUNTANT
  USER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING
}

enum CompanyStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  TRIAL
}

enum CompanySize {
  STARTUP      // 1-10 employees
  SMALL        // 11-50 employees
  MEDIUM       // 51-200 employees
  LARGE        // 201-1000 employees
  ENTERPRISE   // 1000+ employees
}

enum SubscriptionPlan {
  FREE
  STARTER
  PROFESSIONAL
  ENTERPRISE
  CUSTOM
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  EXPIRED
  TRIAL
  PAST_DUE
}

enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  PROPOSAL
  NEGOTIATION
  CLOSED_WON
  CLOSED_LOST
}

enum LeadSource {
  WEBSITE
  REFERRAL
  SOCIAL_MEDIA
  EMAIL_CAMPAIGN
  COLD_CALL
  TRADE_SHOW
  ADVERTISEMENT
  PARTNER
  OTHER
}

enum CustomerStatus {
  ACTIVE
  INACTIVE
  PROSPECT
  CHURNED
}

enum QuotationStatus {
  DRAFT
  SENT
  VIEWED
  ACCEPTED
  REJECTED
  EXPIRED
  REVISED
}

enum InvoiceStatus {
  DRAFT
  SENT
  VIEWED
  PAID
  PARTIALLY_PAID
  OVERDUE
  CANCELLED
  REFUNDED
}

enum ContractStatus {
  DRAFT
  REVIEW
  SENT
  SIGNED
  ACTIVE
  COMPLETED
  CANCELLED
  EXPIRED
  RENEWED
}

enum ContractType {
  SERVICE
  PRODUCT
  SUBSCRIPTION
  MAINTENANCE
  CONSULTING
  LICENSE
  PARTNERSHIP
  NDA
  OTHER
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
  CRITICAL
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  REVIEW
  DONE
  CANCELLED
}

enum ActivityType {
  NOTE
  CALL
  EMAIL
  MEETING
  TASK
  STATUS_CHANGE
  DOCUMENT_UPLOAD
  PAYMENT_RECEIVED
  CONTRACT_SIGNED
  SYSTEM
}

enum TransactionType {
  PAYMENT
  REFUND
  ADJUSTMENT
  FEE
  DISCOUNT
}

enum PaymentMethod {
  CASH
  CHECK
  BANK_TRANSFER
  CREDIT_CARD
  DEBIT_CARD
  PAYPAL
  STRIPE
  CRYPTO
  OTHER
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum DocumentType {
  CONTRACT
  INVOICE
  QUOTATION
  RECEIPT
  AGREEMENT
  PROPOSAL
  REPORT
  IMAGE
  OTHER
}

// ============================================================================
// AUTHENTICATION MODELS (NextAuth.js)
// ============================================================================

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// ============================================================================
// CORE BUSINESS MODELS
// ============================================================================

model User {
  id                String      @id @default(cuid())
  email             String      @unique
  password          String?
  name              String?
  firstName         String?
  lastName          String?
  avatar            String?
  phone             String?
  role              UserRole    @default(USER)
  status            UserStatus  @default(ACTIVE)

  // Profile
  title             String?
  department        String?
  bio               String?
  timezone          String?
  language          String      @default("en")

  // Authentication
  emailVerified     DateTime?
  emailVerificationToken String?
  passwordResetToken String?
  passwordResetExpires DateTime?
  twoFactorEnabled  Boolean     @default(false)
  twoFactorSecret   String?

  // Activity tracking
  lastLoginAt       DateTime?
  loginCount        Int         @default(0)
  lastActiveAt      DateTime?

  // Company relationship
  companyId         String?
  company           Company?    @relation("CompanyMembers", fields: [companyId], references: [id])
  ownedCompany      Company?    @relation("CompanyOwner")

  // Permissions and settings
  permissions       Json?
  settings          Json?
  preferences       Json?

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  accounts          Account[]
  sessions          Session[]

  // Business relations
  createdLeads      Lead[]      @relation("LeadCreatedBy")
  assignedLeads     Lead[]      @relation("LeadAssignedTo")
  createdCustomers  Customer[]  @relation("CustomerCreatedBy")
  assignedCustomers Customer[]  @relation("CustomerAssignedTo")
  createdQuotations Quotation[] @relation("QuotationCreatedBy")
  assignedQuotations Quotation[] @relation("QuotationAssignedTo")
  createdInvoices   Invoice[]   @relation("InvoiceCreatedBy")
  assignedInvoices  Invoice[]   @relation("InvoiceAssignedTo")
  createdContracts  Contract[]  @relation("ContractCreatedBy")
  assignedContracts Contract[]  @relation("ContractAssignedTo")
  createdTasks      Task[]      @relation("TaskCreatedBy")
  assignedTasks     Task[]      @relation("TaskAssignedTo")
  activities        Activity[]
  notes             Note[]
  documents         Document[]
  signatures        Signature[]

  @@map("users")
}

model Company {
  id                String          @id @default(cuid())
  name              String
  email             String?
  phone             String?
  address           String?
  city              String?
  state             String?
  country           String?
  postalCode        String?
  website           String?
  logo              String?

  // Business details
  industry          String?
  size              CompanySize?
  businessType      String?
  taxId             String?
  registrationNumber String?

  // Settings and branding
  settings          Json?
  branding          Json?
  theme             Json?

  // Subscription
  subscriptionId    String?         @unique
  subscription      Subscription?   @relation(fields: [subscriptionId], references: [id])

  // Owner and members
  ownerId           String          @unique
  owner             User            @relation("CompanyOwner", fields: [ownerId], references: [id])
  members           User[]          @relation("CompanyMembers")

  // Status and limits
  status            CompanyStatus   @default(ACTIVE)
  maxUsers          Int             @default(5)
  maxCustomers      Int             @default(100)
  maxQuotations     Int             @default(50)
  maxInvoices       Int             @default(50)
  maxContracts      Int             @default(25)
  maxStorage        BigInt          @default(1073741824) // 1GB in bytes

  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Business relations
  leads             Lead[]
  customers         Customer[]
  quotations        Quotation[]
  invoices          Invoice[]
  contracts         Contract[]
  items             Item[]
  tasks             Task[]
  activities        Activity[]
  notes             Note[]
  documents         Document[]
  signatures        Signature[]
  transactions      Transaction[]

  @@map("companies")
}

model Subscription {
  id                String              @id @default(cuid())
  plan              SubscriptionPlan
  status            SubscriptionStatus  @default(TRIAL)

  // Billing
  priceId           String?
  customerId        String?             // Stripe customer ID
  subscriptionId    String?             // Stripe subscription ID

  // Dates
  startDate         DateTime            @default(now())
  endDate           DateTime?
  trialEndDate      DateTime?
  cancelledAt       DateTime?

  // Billing cycle
  billingCycle      String              @default("monthly") // monthly, yearly
  amount            Decimal             @default(0)
  currency          String              @default("USD")

  // Features
  features          Json?
  limits            Json?

  // Timestamps
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  company           Company?

  @@map("subscriptions")
}

// ============================================================================
// CRM MODELS
// ============================================================================

model Lead {
  id                String      @id @default(cuid())
  firstName         String
  lastName          String
  email             String
  phone             String?
  companyName       String?
  title             String?
  website           String?

  // Lead details
  source            LeadSource  @default(OTHER)
  status            LeadStatus  @default(NEW)
  priority          Priority    @default(MEDIUM)

  // Address
  address           String?
  city              String?
  state             String?
  country           String?
  postalCode        String?

  // Business info
  industry          String?
  companySize       String?
  budget            Decimal?
  timeline          String?

  // Lead scoring and qualification
  score             Int         @default(0)
  qualified         Boolean     @default(false)
  qualifiedAt       DateTime?

  // Conversion
  convertedAt       DateTime?
  customerId        String?
  customer          Customer?   @relation(fields: [customerId], references: [id])

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?       @relation("LeadAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User        @relation("LeadCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company     @relation(fields: [companyId], references: [id])

  // Additional data
  description       String?
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  activities        Activity[]
  leadNotes         Note[]      @relation("LeadNotes")
  tasks             Task[]
  documents         Document[]

  @@map("leads")
}

model Customer {
  id                String          @id @default(cuid())
  name              String
  email             String?
  phone             String?
  companyName       String?
  title             String?
  website           String?

  // Address
  address           String?
  city              String?
  state             String?
  country           String?
  postalCode        String?

  // Business info
  industry          String?
  companySize       String?
  taxId             String?

  // Customer details
  status            CustomerStatus  @default(ACTIVE)
  priority          Priority        @default(MEDIUM)

  // Financial
  creditLimit       Decimal?
  paymentTerms      String?

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?           @relation("CustomerAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User            @relation("CustomerCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company         @relation(fields: [companyId], references: [id])

  // Additional data
  description       String?
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Relations
  leads             Lead[]
  quotations        Quotation[]
  invoices          Invoice[]
  contracts         Contract[]
  activities        Activity[]
  customerNotes     Note[]      @relation("CustomerNotes")
  tasks             Task[]
  documents         Document[]

  @@map("customers")
}

// ============================================================================
// PRODUCT/SERVICE MODELS
// ============================================================================

model Item {
  id                String      @id @default(cuid())
  name              String
  description       String?
  sku               String?     @unique
  category          String?

  // Pricing
  unitPrice         Decimal     @default(0)
  costPrice         Decimal?
  currency          String      @default("USD")

  // Inventory
  trackInventory    Boolean     @default(false)
  stockQuantity     Int?
  lowStockAlert     Int?

  // Tax and accounting
  taxable           Boolean     @default(true)
  taxRate           Decimal     @default(0)
  accountingCode    String?

  // Status
  active            Boolean     @default(true)

  // Company relationship
  companyId         String
  company           Company     @relation(fields: [companyId], references: [id])

  // Additional data
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  quotationItems    QuotationItem[]
  invoiceItems      InvoiceItem[]

  @@map("items")
}

// ============================================================================
// SALES MODELS
// ============================================================================

model Quotation {
  id                String          @id @default(cuid())
  quotationNumber   String          @unique
  title             String
  description       String?

  // Customer relationship
  customerId        String
  customer          Customer        @relation(fields: [customerId], references: [id])

  // Status and dates
  status            QuotationStatus @default(DRAFT)
  validUntil        DateTime?
  acceptedAt        DateTime?
  rejectedAt        DateTime?

  // Financial
  subtotal          Decimal         @default(0)
  taxRate           Decimal         @default(0)
  taxAmount         Decimal         @default(0)
  discountType      String          @default("PERCENTAGE") // PERCENTAGE or FIXED
  discountValue     Decimal         @default(0)
  discountAmount    Decimal         @default(0)
  total             Decimal         @default(0)
  currency          String          @default("USD")

  // Terms and conditions
  terms             String?
  internalNotes     String?
  paymentTerms      String?

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?           @relation("QuotationAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User            @relation("QuotationCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company         @relation(fields: [companyId], references: [id])

  // Additional data
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Relations
  items             QuotationItem[]
  invoices          Invoice[]
  contracts         Contract[]
  activities        Activity[]
  quotationNotes    Note[]      @relation("QuotationNotes")
  tasks             Task[]
  documents         Document[]
  signatures        Signature[]

  @@map("quotations")
}

model QuotationItem {
  id                String      @id @default(cuid())
  name              String
  description       String?
  quantity          Int         @default(1)
  unitPrice         Decimal     @default(0)
  discount          Decimal     @default(0) // Percentage
  taxRate           Decimal     @default(0) // Percentage
  total             Decimal     @default(0)

  // Item relationship
  itemId            String?
  item              Item?       @relation(fields: [itemId], references: [id])

  // Quotation relationship
  quotationId       String
  quotation         Quotation   @relation(fields: [quotationId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  @@map("quotation_items")
}

// ============================================================================
// INVOICING MODELS
// ============================================================================

model Invoice {
  id                String        @id @default(cuid())
  invoiceNumber     String        @unique
  title             String?
  description       String?

  // Customer relationship
  customerId        String
  customer          Customer      @relation(fields: [customerId], references: [id])

  // Quotation relationship
  quotationId       String?
  quotation         Quotation?    @relation(fields: [quotationId], references: [id])

  // Status and dates
  status            InvoiceStatus @default(DRAFT)
  issueDate         DateTime      @default(now())
  dueDate           DateTime?
  paidAt            DateTime?

  // Financial
  subtotal          Decimal       @default(0)
  taxRate           Decimal       @default(0)
  taxAmount         Decimal       @default(0)
  discountType      String        @default("PERCENTAGE") // PERCENTAGE or FIXED
  discountValue     Decimal       @default(0)
  discountAmount    Decimal       @default(0)
  total             Decimal       @default(0)
  paidAmount        Decimal       @default(0)
  balanceAmount     Decimal       @default(0)
  currency          String        @default("USD")

  // Terms and conditions
  terms             String?
  internalNotes     String?
  paymentTerms      String?
  paymentMethod     String?

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?         @relation("InvoiceAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User          @relation("InvoiceCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company       @relation(fields: [companyId], references: [id])

  // Additional data
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  items             InvoiceItem[]
  contracts         Contract[]
  transactions      Transaction[]
  activities        Activity[]
  invoiceNotes      Note[]      @relation("InvoiceNotes")
  tasks             Task[]
  documents         Document[]

  @@map("invoices")
}

model InvoiceItem {
  id                String      @id @default(cuid())
  name              String
  description       String?
  quantity          Int         @default(1)
  unitPrice         Decimal     @default(0)
  discount          Decimal     @default(0) // Percentage
  taxRate           Decimal     @default(0) // Percentage
  total             Decimal     @default(0)

  // Item relationship
  itemId            String?
  item              Item?       @relation(fields: [itemId], references: [id])

  // Invoice relationship
  invoiceId         String
  invoice           Invoice     @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  @@map("invoice_items")
}

// ============================================================================
// CONTRACT MODELS
// ============================================================================

model Contract {
  id                String          @id @default(cuid())
  contractNumber    String          @unique
  title             String
  description       String?
  type              ContractType    @default(SERVICE)

  // Customer relationship
  customerId        String
  customer          Customer        @relation(fields: [customerId], references: [id])

  // Related documents
  quotationId       String?
  quotation         Quotation?      @relation(fields: [quotationId], references: [id])
  invoiceId         String?
  invoice           Invoice?        @relation(fields: [invoiceId], references: [id])

  // Status and dates
  status            ContractStatus  @default(DRAFT)
  priority          Priority        @default(MEDIUM)
  startDate         DateTime?
  endDate           DateTime?
  renewalDate       DateTime?
  autoRenewal       Boolean         @default(false)
  renewalPeriod     Int?            // in months
  signedAt          DateTime?

  // Financial
  value             Decimal?
  currency          String          @default("USD")

  // Terms and conditions
  terms             String?
  conditions        String?
  internalNotes     String?

  // Signature requirements
  signatureRequired Boolean         @default(true)

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?           @relation("ContractAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User            @relation("ContractCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company         @relation(fields: [companyId], references: [id])

  // Additional data
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Relations
  signatures        Signature[]
  activities        Activity[]
  contractNotes     Note[]      @relation("ContractNotes")
  tasks             Task[]
  documents         Document[]

  @@map("contracts")
}

// ============================================================================
// TASK MANAGEMENT MODELS
// ============================================================================

model Task {
  id                String      @id @default(cuid())
  title             String
  description       String?
  status            TaskStatus  @default(TODO)
  priority          Priority    @default(MEDIUM)

  // Dates
  dueDate           DateTime?
  startDate         DateTime?
  completedAt       DateTime?

  // Assignment and ownership
  assignedToId      String?
  assignedTo        User?       @relation("TaskAssignedTo", fields: [assignedToId], references: [id])
  createdById       String
  createdBy         User        @relation("TaskCreatedBy", fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company     @relation(fields: [companyId], references: [id])

  // Related entities
  leadId            String?
  lead              Lead?       @relation(fields: [leadId], references: [id])
  customerId        String?
  customer          Customer?   @relation(fields: [customerId], references: [id])
  quotationId       String?
  quotation         Quotation?  @relation(fields: [quotationId], references: [id])
  invoiceId         String?
  invoice           Invoice?    @relation(fields: [invoiceId], references: [id])
  contractId        String?
  contract          Contract?   @relation(fields: [contractId], references: [id])

  // Additional data
  tags              Json?
  customFields      Json?

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  activities        Activity[]
  notes             Note[]
  documents         Document[]

  @@map("tasks")
}

// ============================================================================
// ACTIVITY AND COMMUNICATION MODELS
// ============================================================================

model Activity {
  id                String        @id @default(cuid())
  type              ActivityType
  title             String
  description       String?

  // User relationship
  createdById       String
  createdBy         User          @relation(fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company       @relation(fields: [companyId], references: [id])

  // Related entities
  leadId            String?
  lead              Lead?         @relation(fields: [leadId], references: [id])
  customerId        String?
  customer          Customer?     @relation(fields: [customerId], references: [id])
  quotationId       String?
  quotation         Quotation?    @relation(fields: [quotationId], references: [id])
  invoiceId         String?
  invoice           Invoice?      @relation(fields: [invoiceId], references: [id])
  contractId        String?
  contract          Contract?     @relation(fields: [contractId], references: [id])
  taskId            String?
  task              Task?         @relation(fields: [taskId], references: [id])

  // Additional data
  metadata          Json?

  // Timestamps
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  @@map("activities")
}

model Note {
  id                String      @id @default(cuid())
  title             String?
  content           String

  // User relationship
  createdById       String
  createdBy         User        @relation(fields: [createdById], references: [id])

  // Company relationship
  companyId         String
  company           Company     @relation(fields: [companyId], references: [id])

  // Related entities
  leadId            String?
  lead              Lead?       @relation("LeadNotes", fields: [leadId], references: [id])
  customerId        String?
  customer          Customer?   @relation("CustomerNotes", fields: [customerId], references: [id])
  quotationId       String?
  quotation         Quotation?  @relation("QuotationNotes", fields: [quotationId], references: [id])
  invoiceId         String?
  invoice           Invoice?    @relation("InvoiceNotes", fields: [invoiceId], references: [id])
  contractId        String?
  contract          Contract?   @relation("ContractNotes", fields: [contractId], references: [id])
  taskId            String?
  task              Task?       @relation(fields: [taskId], references: [id])

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  @@map("notes")
}

// ============================================================================
// DOCUMENT MANAGEMENT MODELS
// ============================================================================

model Document {
  id                String        @id @default(cuid())
  name              String
  originalName      String
  type              DocumentType  @default(OTHER)
  mimeType          String
  size              BigInt
  url               String

  // Storage
  storageProvider   String        @default("local") // local, s3, gcs, etc.
  storagePath       String

  // User relationship
  uploadedById      String
  uploadedBy        User          @relation(fields: [uploadedById], references: [id])

  // Company relationship
  companyId         String
  company           Company       @relation(fields: [companyId], references: [id])

  // Related entities
  leadId            String?
  lead              Lead?         @relation(fields: [leadId], references: [id])
  customerId        String?
  customer          Customer?     @relation(fields: [customerId], references: [id])
  quotationId       String?
  quotation         Quotation?    @relation(fields: [quotationId], references: [id])
  invoiceId         String?
  invoice           Invoice?      @relation(fields: [invoiceId], references: [id])
  contractId        String?
  contract          Contract?     @relation(fields: [contractId], references: [id])
  taskId            String?
  task              Task?         @relation(fields: [taskId], references: [id])

  // Additional data
  tags              Json?
  metadata          Json?

  // Timestamps
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  @@map("documents")
}

model Signature {
  id                String      @id @default(cuid())
  signatureData     String      // Base64 encoded signature or signature provider ID
  signatureType     String      @default("electronic") // electronic, digital, wet

  // Signer information
  signerName        String
  signerEmail       String
  signerIp          String?
  signedAt          DateTime    @default(now())

  // Signature provider (DocuSign, Adobe Sign, etc.)
  provider          String?
  providerSignatureId String?

  // User relationship (internal user who requested signature)
  requestedById     String
  requestedBy       User        @relation(fields: [requestedById], references: [id])

  // Company relationship
  companyId         String
  company           Company     @relation(fields: [companyId], references: [id])

  // Related entities
  quotationId       String?
  quotation         Quotation?  @relation(fields: [quotationId], references: [id])
  contractId        String?
  contract          Contract?   @relation(fields: [contractId], references: [id])

  // Additional data
  metadata          Json?

  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  @@map("signatures")
}

// ============================================================================
// FINANCIAL MODELS
// ============================================================================

model Transaction {
  id                String          @id @default(cuid())
  type              TransactionType
  amount            Decimal
  currency          String          @default("USD")

  // Payment details
  paymentMethod     PaymentMethod?
  paymentStatus     PaymentStatus   @default(PENDING)

  // Transaction details
  reference         String?
  description       String?
  notes             String?

  // External payment provider
  providerId        String?         // Stripe payment intent ID, etc.
  providerData      Json?

  // Dates
  transactionDate   DateTime        @default(now())
  processedAt       DateTime?

  // Company relationship
  companyId         String
  company           Company         @relation(fields: [companyId], references: [id])

  // Related entities
  invoiceId         String?
  invoice           Invoice?        @relation(fields: [invoiceId], references: [id])

  // Additional data
  metadata          Json?

  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  @@map("transactions")
}
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contracts/route";
exports.ids = ["app/api/contracts/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontracts%2Froute&page=%2Fapi%2Fcontracts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontracts%2Froute&page=%2Fapi%2Fcontracts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_contracts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/contracts/route.ts */ \"(rsc)/./app/api/contracts/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contracts/route\",\n        pathname: \"/api/contracts\",\n        filename: \"route\",\n        bundlePath: \"app/api/contracts/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\contracts\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_contracts_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/contracts/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontracts%2Froute&page=%2Fapi%2Fcontracts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/contracts/route.ts":
/*!************************************!*\
  !*** ./app/api/contracts/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for contract creation/update\nconst contractSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Title is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Customer is required\"),\n    quotationId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    invoiceId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    type: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"SERVICE\",\n        \"PRODUCT\",\n        \"SUBSCRIPTION\",\n        \"MAINTENANCE\",\n        \"CONSULTING\",\n        \"OTHER\"\n    ]).default(\"SERVICE\"),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"DRAFT\",\n        \"REVIEW\",\n        \"SENT\",\n        \"SIGNED\",\n        \"ACTIVE\",\n        \"COMPLETED\",\n        \"CANCELLED\",\n        \"EXPIRED\"\n    ]).default(\"DRAFT\"),\n    value: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Contract value must be positive\").optional().nullable(),\n    currency: zod__WEBPACK_IMPORTED_MODULE_4__.string().default(\"USD\"),\n    startDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    renewalDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    autoRenewal: zod__WEBPACK_IMPORTED_MODULE_4__.boolean().default(false),\n    renewalPeriod: zod__WEBPACK_IMPORTED_MODULE_4__.number().optional().nullable(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    conditions: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    templateId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    signatureRequired: zod__WEBPACK_IMPORTED_MODULE_4__.boolean().default(true),\n    priority: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"LOW\",\n        \"MEDIUM\",\n        \"HIGH\",\n        \"URGENT\"\n    ]).default(\"MEDIUM\"),\n    tags: zod__WEBPACK_IMPORTED_MODULE_4__.array(zod__WEBPACK_IMPORTED_MODULE_4__.string()).optional().default([])\n});\n// GET /api/contracts - List contracts with filtering and pagination\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const search = searchParams.get(\"search\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const type = searchParams.get(\"type\") || \"\";\n        const customerId = searchParams.get(\"customerId\") || \"\";\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            companyId: session.user.companyId || undefined\n        };\n        if (search) {\n            where.OR = [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    contractNumber: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    customer: {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                },\n                {\n                    customer: {\n                        company: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ];\n        }\n        if (status) {\n            where.status = status;\n        }\n        if (type) {\n            where.type = type;\n        }\n        if (customerId) {\n            where.customerId = customerId;\n        }\n        // Get contracts with pagination\n        const [contracts, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.findMany({\n                where,\n                skip,\n                take: limit,\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    invoice: {\n                        select: {\n                            id: true,\n                            invoiceNumber: true\n                        }\n                    },\n                    template: {\n                        select: {\n                            id: true,\n                            name: true,\n                            type: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    assignedTo: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            activities: true,\n                            signatures: true,\n                            documents: true\n                        }\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.count({\n                where\n            })\n        ]);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            contracts,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching contracts:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch contracts\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/contracts - Create new contract\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = contractSchema.parse(body);\n        // Generate contract number\n        const currentYear = new Date().getFullYear();\n        const lastContract = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.contract.findFirst({\n            where: {\n                companyId: session.user.companyId || undefined,\n                contractNumber: {\n                    startsWith: `CON-${currentYear}-`\n                }\n            },\n            orderBy: {\n                contractNumber: \"desc\"\n            }\n        });\n        let nextNumber = 1;\n        if (lastContract) {\n            const lastNumber = parseInt(lastContract.contractNumber.split(\"-\")[2]);\n            nextNumber = lastNumber + 1;\n        }\n        const contractNumber = `CON-${currentYear}-${nextNumber.toString().padStart(4, \"0\")}`;\n        // Prepare contract data\n        const contractData = {\n            ...validatedData,\n            contractNumber,\n            companyId: session.user.companyId,\n            createdById: session.user.id,\n            assignedToId: session.user.id // Default to creator\n        };\n        if (validatedData.startDate) {\n            contractData.startDate = new Date(validatedData.startDate);\n        }\n        if (validatedData.endDate) {\n            contractData.endDate = new Date(validatedData.endDate);\n        }\n        if (validatedData.renewalDate) {\n            contractData.renewalDate = new Date(validatedData.renewalDate);\n        }\n        // Create contract in a transaction\n        const contract = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            const newContract = await tx.contract.create({\n                data: contractData,\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    invoice: {\n                        select: {\n                            id: true,\n                            invoiceNumber: true\n                        }\n                    },\n                    template: {\n                        select: {\n                            id: true,\n                            name: true,\n                            type: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    assignedTo: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    }\n                }\n            });\n            // Log activity\n            await tx.activity.create({\n                data: {\n                    type: \"NOTE\",\n                    title: \"Contract Created\",\n                    description: `Contract \"${newContract.title}\" (${contractNumber}) was created`,\n                    contractId: newContract.id,\n                    customerId: newContract.customerId,\n                    quotationId: newContract.quotationId,\n                    invoiceId: newContract.invoiceId,\n                    companyId: session.user.companyId,\n                    createdById: session.user.id\n                }\n            });\n            return newContract;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(contract, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error creating contract:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create contract\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/contracts/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    },\n                    include: {\n                        company: true,\n                        ownedCompany: true\n                    }\n                });\n                if (!user || !user.password) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                // Update last login\n                await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.update({\n                    where: {\n                        id: user.id\n                    },\n                    data: {\n                        lastLoginAt: new Date(),\n                        loginCount: {\n                            increment: 1\n                        }\n                    }\n                });\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    companyId: user.companyId,\n                    company: user.company || user.ownedCompany\n                };\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.companyId = user.companyId;\n                token.company = user.company;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                session.user.company = token.company;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUN5RDtBQUNRO0FBQ1Y7QUFDQTtBQUMxQjtBQUNJO0FBRTFCLE1BQU1NLGNBQStCO0lBQzFDQyxTQUFTUCx3RUFBYUEsQ0FBQ0ssMkNBQU1BO0lBQzdCRyxXQUFXO1FBQ1RQLDJFQUFtQkEsQ0FBQztZQUNsQlEsTUFBTTtZQUNOQyxhQUFhO2dCQUNYQyxPQUFPO29CQUFFQyxPQUFPO29CQUFTQyxNQUFNO2dCQUFRO2dCQUN2Q0MsVUFBVTtvQkFBRUYsT0FBTztvQkFBWUMsTUFBTTtnQkFBVztZQUNsRDtZQUNBLE1BQU1FLFdBQVVMLFdBQVc7Z0JBQ3pCLElBQUksQ0FBQ0EsYUFBYUMsU0FBUyxDQUFDRCxhQUFhSSxVQUFVO29CQUNqRCxPQUFPO2dCQUNUO2dCQUVBLE1BQU1FLE9BQU8sTUFBTVgsMkNBQU1BLENBQUNXLElBQUksQ0FBQ0MsVUFBVSxDQUFDO29CQUN4Q0MsT0FBTzt3QkFDTFAsT0FBT0QsWUFBWUMsS0FBSztvQkFDMUI7b0JBQ0FRLFNBQVM7d0JBQ1BDLFNBQVM7d0JBQ1RDLGNBQWM7b0JBQ2hCO2dCQUNGO2dCQUVBLElBQUksQ0FBQ0wsUUFBUSxDQUFDQSxLQUFLRixRQUFRLEVBQUU7b0JBQzNCLE9BQU87Z0JBQ1Q7Z0JBRUEsTUFBTVEsa0JBQWtCLE1BQU1sQix1REFBYyxDQUMxQ00sWUFBWUksUUFBUSxFQUNwQkUsS0FBS0YsUUFBUTtnQkFHZixJQUFJLENBQUNRLGlCQUFpQjtvQkFDcEIsT0FBTztnQkFDVDtnQkFFQSxvQkFBb0I7Z0JBQ3BCLE1BQU1qQiwyQ0FBTUEsQ0FBQ1csSUFBSSxDQUFDUSxNQUFNLENBQUM7b0JBQ3ZCTixPQUFPO3dCQUFFTyxJQUFJVCxLQUFLUyxFQUFFO29CQUFDO29CQUNyQkMsTUFBTTt3QkFDSkMsYUFBYSxJQUFJQzt3QkFDakJDLFlBQVk7NEJBQUVDLFdBQVc7d0JBQUU7b0JBQzdCO2dCQUNGO2dCQUVBLE9BQU87b0JBQ0xMLElBQUlULEtBQUtTLEVBQUU7b0JBQ1hkLE9BQU9LLEtBQUtMLEtBQUs7b0JBQ2pCRixNQUFNTyxLQUFLUCxJQUFJO29CQUNmc0IsTUFBTWYsS0FBS2UsSUFBSTtvQkFDZkMsV0FBV2hCLEtBQUtnQixTQUFTO29CQUN6QlosU0FBU0osS0FBS0ksT0FBTyxJQUFJSixLQUFLSyxZQUFZO2dCQUM1QztZQUNGO1FBQ0Y7UUFDQW5CLHNFQUFjQSxDQUFDO1lBQ2IrQixVQUFVQyxRQUFRQyxHQUFHLENBQUNDLGdCQUFnQjtZQUN0Q0MsY0FBY0gsUUFBUUMsR0FBRyxDQUFDRyxvQkFBb0I7UUFDaEQ7UUFDQW5DLHNFQUFjQSxDQUFDO1lBQ2I4QixVQUFVQyxRQUFRQyxHQUFHLENBQUNJLGdCQUFnQjtZQUN0Q0YsY0FBY0gsUUFBUUMsR0FBRyxDQUFDSyxvQkFBb0I7UUFDaEQ7S0FDRDtJQUNEQyxTQUFTO1FBQ1BDLFVBQVU7SUFDWjtJQUNBQyxXQUFXO1FBQ1QsTUFBTUMsS0FBSSxFQUFFQyxLQUFLLEVBQUU3QixJQUFJLEVBQUU7WUFDdkIsSUFBSUEsTUFBTTtnQkFDUjZCLE1BQU1kLElBQUksR0FBR2YsS0FBS2UsSUFBSTtnQkFDdEJjLE1BQU1iLFNBQVMsR0FBR2hCLEtBQUtnQixTQUFTO2dCQUNoQ2EsTUFBTXpCLE9BQU8sR0FBR0osS0FBS0ksT0FBTztZQUM5QjtZQUNBLE9BQU95QjtRQUNUO1FBQ0EsTUFBTUosU0FBUSxFQUFFQSxPQUFPLEVBQUVJLEtBQUssRUFBRTtZQUM5QixJQUFJQSxPQUFPO2dCQUNUSixRQUFRekIsSUFBSSxDQUFDUyxFQUFFLEdBQUdvQixNQUFNQyxHQUFHO2dCQUMzQkwsUUFBUXpCLElBQUksQ0FBQ2UsSUFBSSxHQUFHYyxNQUFNZCxJQUFJO2dCQUM5QlUsUUFBUXpCLElBQUksQ0FBQ2dCLFNBQVMsR0FBR2EsTUFBTWIsU0FBUztnQkFDeENTLFFBQVF6QixJQUFJLENBQUNJLE9BQU8sR0FBR3lCLE1BQU16QixPQUFPO1lBQ3RDO1lBQ0EsT0FBT3FCO1FBQ1Q7SUFDRjtJQUNBTSxPQUFPO1FBQ0xDLFFBQVE7UUFDUkMsUUFBUTtRQUNSQyxPQUFPO0lBQ1Q7QUFDRixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNhYXMtYXBwLy4vbGliL2F1dGgudHM/YmY3ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0QXV0aE9wdGlvbnMgfSBmcm9tICduZXh0LWF1dGgnXG5pbXBvcnQgeyBQcmlzbWFBZGFwdGVyIH0gZnJvbSAnQG5leHQtYXV0aC9wcmlzbWEtYWRhcHRlcidcbmltcG9ydCBDcmVkZW50aWFsc1Byb3ZpZGVyIGZyb20gJ25leHQtYXV0aC9wcm92aWRlcnMvY3JlZGVudGlhbHMnXG5pbXBvcnQgR29vZ2xlUHJvdmlkZXIgZnJvbSAnbmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGUnXG5pbXBvcnQgR2l0SHViUHJvdmlkZXIgZnJvbSAnbmV4dC1hdXRoL3Byb3ZpZGVycy9naXRodWInXG5pbXBvcnQgYmNyeXB0IGZyb20gJ2JjcnlwdGpzJ1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnLi9wcmlzbWEnXG5cbmV4cG9ydCBjb25zdCBhdXRoT3B0aW9uczogTmV4dEF1dGhPcHRpb25zID0ge1xuICBhZGFwdGVyOiBQcmlzbWFBZGFwdGVyKHByaXNtYSksXG4gIHByb3ZpZGVyczogW1xuICAgIENyZWRlbnRpYWxzUHJvdmlkZXIoe1xuICAgICAgbmFtZTogJ2NyZWRlbnRpYWxzJyxcbiAgICAgIGNyZWRlbnRpYWxzOiB7XG4gICAgICAgIGVtYWlsOiB7IGxhYmVsOiAnRW1haWwnLCB0eXBlOiAnZW1haWwnIH0sXG4gICAgICAgIHBhc3N3b3JkOiB7IGxhYmVsOiAnUGFzc3dvcmQnLCB0eXBlOiAncGFzc3dvcmQnIH1cbiAgICAgIH0sXG4gICAgICBhc3luYyBhdXRob3JpemUoY3JlZGVudGlhbHMpIHtcbiAgICAgICAgaWYgKCFjcmVkZW50aWFscz8uZW1haWwgfHwgIWNyZWRlbnRpYWxzPy5wYXNzd29yZCkge1xuICAgICAgICAgIHJldHVybiBudWxsXG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCB1c2VyID0gYXdhaXQgcHJpc21hLnVzZXIuZmluZFVuaXF1ZSh7XG4gICAgICAgICAgd2hlcmU6IHtcbiAgICAgICAgICAgIGVtYWlsOiBjcmVkZW50aWFscy5lbWFpbFxuICAgICAgICAgIH0sXG4gICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgY29tcGFueTogdHJ1ZSxcbiAgICAgICAgICAgIG93bmVkQ29tcGFueTogdHJ1ZVxuICAgICAgICAgIH1cbiAgICAgICAgfSlcblxuICAgICAgICBpZiAoIXVzZXIgfHwgIXVzZXIucGFzc3dvcmQpIHtcbiAgICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgaXNQYXNzd29yZFZhbGlkID0gYXdhaXQgYmNyeXB0LmNvbXBhcmUoXG4gICAgICAgICAgY3JlZGVudGlhbHMucGFzc3dvcmQsXG4gICAgICAgICAgdXNlci5wYXNzd29yZFxuICAgICAgICApXG5cbiAgICAgICAgaWYgKCFpc1Bhc3N3b3JkVmFsaWQpIHtcbiAgICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgICB9XG5cbiAgICAgICAgLy8gVXBkYXRlIGxhc3QgbG9naW5cbiAgICAgICAgYXdhaXQgcHJpc21hLnVzZXIudXBkYXRlKHtcbiAgICAgICAgICB3aGVyZTogeyBpZDogdXNlci5pZCB9LFxuICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgIGxhc3RMb2dpbkF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgbG9naW5Db3VudDogeyBpbmNyZW1lbnQ6IDEgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSlcblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlkOiB1c2VyLmlkLFxuICAgICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxuICAgICAgICAgIG5hbWU6IHVzZXIubmFtZSxcbiAgICAgICAgICByb2xlOiB1c2VyLnJvbGUsXG4gICAgICAgICAgY29tcGFueUlkOiB1c2VyLmNvbXBhbnlJZCxcbiAgICAgICAgICBjb21wYW55OiB1c2VyLmNvbXBhbnkgfHwgdXNlci5vd25lZENvbXBhbnlcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pLFxuICAgIEdvb2dsZVByb3ZpZGVyKHtcbiAgICAgIGNsaWVudElkOiBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX0lEISxcbiAgICAgIGNsaWVudFNlY3JldDogcHJvY2Vzcy5lbnYuR09PR0xFX0NMSUVOVF9TRUNSRVQhLFxuICAgIH0pLFxuICAgIEdpdEh1YlByb3ZpZGVyKHtcbiAgICAgIGNsaWVudElkOiBwcm9jZXNzLmVudi5HSVRIVUJfQ0xJRU5UX0lEISxcbiAgICAgIGNsaWVudFNlY3JldDogcHJvY2Vzcy5lbnYuR0lUSFVCX0NMSUVOVF9TRUNSRVQhLFxuICAgIH0pXG4gIF0sXG4gIHNlc3Npb246IHtcbiAgICBzdHJhdGVneTogJ2p3dCdcbiAgfSxcbiAgY2FsbGJhY2tzOiB7XG4gICAgYXN5bmMgand0KHsgdG9rZW4sIHVzZXIgfSkge1xuICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgdG9rZW4ucm9sZSA9IHVzZXIucm9sZVxuICAgICAgICB0b2tlbi5jb21wYW55SWQgPSB1c2VyLmNvbXBhbnlJZFxuICAgICAgICB0b2tlbi5jb21wYW55ID0gdXNlci5jb21wYW55XG4gICAgICB9XG4gICAgICByZXR1cm4gdG9rZW5cbiAgICB9LFxuICAgIGFzeW5jIHNlc3Npb24oeyBzZXNzaW9uLCB0b2tlbiB9KSB7XG4gICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgc2Vzc2lvbi51c2VyLmlkID0gdG9rZW4uc3ViIVxuICAgICAgICBzZXNzaW9uLnVzZXIucm9sZSA9IHRva2VuLnJvbGUgYXMgc3RyaW5nXG4gICAgICAgIHNlc3Npb24udXNlci5jb21wYW55SWQgPSB0b2tlbi5jb21wYW55SWQgYXMgc3RyaW5nXG4gICAgICAgIHNlc3Npb24udXNlci5jb21wYW55ID0gdG9rZW4uY29tcGFueSBhcyBhbnlcbiAgICAgIH1cbiAgICAgIHJldHVybiBzZXNzaW9uXG4gICAgfVxuICB9LFxuICBwYWdlczoge1xuICAgIHNpZ25JbjogJy9hdXRoL3NpZ25pbicsXG4gICAgc2lnblVwOiAnL2F1dGgvc2lnbnVwJyxcbiAgICBlcnJvcjogJy9hdXRoL2Vycm9yJ1xuICB9XG59XG4iXSwibmFtZXMiOlsiUHJpc21hQWRhcHRlciIsIkNyZWRlbnRpYWxzUHJvdmlkZXIiLCJHb29nbGVQcm92aWRlciIsIkdpdEh1YlByb3ZpZGVyIiwiYmNyeXB0IiwicHJpc21hIiwiYXV0aE9wdGlvbnMiLCJhZGFwdGVyIiwicHJvdmlkZXJzIiwibmFtZSIsImNyZWRlbnRpYWxzIiwiZW1haWwiLCJsYWJlbCIsInR5cGUiLCJwYXNzd29yZCIsImF1dGhvcml6ZSIsInVzZXIiLCJmaW5kVW5pcXVlIiwid2hlcmUiLCJpbmNsdWRlIiwiY29tcGFueSIsIm93bmVkQ29tcGFueSIsImlzUGFzc3dvcmRWYWxpZCIsImNvbXBhcmUiLCJ1cGRhdGUiLCJpZCIsImRhdGEiLCJsYXN0TG9naW5BdCIsIkRhdGUiLCJsb2dpbkNvdW50IiwiaW5jcmVtZW50Iiwicm9sZSIsImNvbXBhbnlJZCIsImNsaWVudElkIiwicHJvY2VzcyIsImVudiIsIkdPT0dMRV9DTElFTlRfSUQiLCJjbGllbnRTZWNyZXQiLCJHT09HTEVfQ0xJRU5UX1NFQ1JFVCIsIkdJVEhVQl9DTElFTlRfSUQiLCJHSVRIVUJfQ0xJRU5UX1NFQ1JFVCIsInNlc3Npb24iLCJzdHJhdGVneSIsImNhbGxiYWNrcyIsImp3dCIsInRva2VuIiwic3ViIiwicGFnZXMiLCJzaWduSW4iLCJzaWduVXAiLCJlcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontracts%2Froute&page=%2Fapi%2Fcontracts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontracts%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
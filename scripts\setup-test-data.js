const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('🚀 Setting up enterprise test data...')

    // Check if test user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingUser) {
      console.log('✅ Test user already exists')
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Password: password123')
      return
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('password123', 12)

    // Create test company with owner
    const company = await prisma.company.create({
      data: {
        name: 'Acme Corporation',
        email: '<EMAIL>',
        phone: '******-0123',
        address: '123 Business Street',
        city: 'San Francisco',
        state: 'CA',
        country: 'USA',
        postalCode: '94105',
        website: 'https://acme.com',
        industry: 'Technology',
        size: 'MEDIUM',
        status: 'ACTIVE',
        maxUsers: 50,
        maxCustomers: 1000,
        maxQuotations: 500,
        maxInvoices: 500,
        maxContracts: 100,
        owner: {
          create: {
            email: '<EMAIL>',
            password: hashedPassword,
            name: 'John Admin',
            firstName: 'John',
            lastName: 'Admin',
            role: 'ADMIN',
            status: 'ACTIVE',
            title: 'CEO',
            department: 'Executive',
            emailVerified: new Date(),
            loginCount: 0
          }
        }
      },
      include: {
        owner: true
      }
    })

    console.log('✅ Created company:', company.name)
    const user = company.owner

    console.log('✅ Created user:', user.name)

    // Create additional team members
    const salesUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Sarah Sales',
        firstName: 'Sarah',
        lastName: 'Sales',
        role: 'SALES',
        status: 'ACTIVE',
        title: 'Sales Manager',
        department: 'Sales',
        companyId: company.id,
        emailVerified: new Date(),
        loginCount: 0
      }
    })

    const accountantUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Mike Accountant',
        firstName: 'Mike',
        lastName: 'Accountant',
        role: 'ACCOUNTANT',
        status: 'ACTIVE',
        title: 'Chief Financial Officer',
        department: 'Finance',
        companyId: company.id,
        emailVerified: new Date(),
        loginCount: 0
      }
    })

    console.log('✅ Created team members:', salesUser.name, accountantUser.name)

    // Create some test leads
    const lead1 = await prisma.lead.create({
      data: {
        firstName: 'Alice',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '******-1001',
        companyName: 'Potential Corp',
        title: 'CTO',
        website: 'https://potential.com',
        source: 'WEBSITE',
        status: 'NEW',
        priority: 'HIGH',
        address: '100 Potential St',
        city: 'Tech City',
        state: 'CA',
        country: 'USA',
        postalCode: '90210',
        industry: 'Software',
        budget: 50000,
        timeline: 'Q1 2024',
        score: 85,
        assignedToId: salesUser.id,
        createdById: user.id,
        companyId: company.id,
        description: 'Interested in our enterprise solution'
      }
    })

    const lead2 = await prisma.lead.create({
      data: {
        firstName: 'Bob',
        lastName: 'Wilson',
        email: '<EMAIL>',
        phone: '******-1002',
        companyName: 'Startup Inc',
        title: 'Founder',
        website: 'https://startup.io',
        source: 'REFERRAL',
        status: 'CONTACTED',
        priority: 'MEDIUM',
        address: '200 Innovation Ave',
        city: 'Silicon Valley',
        state: 'CA',
        country: 'USA',
        postalCode: '94301',
        industry: 'Fintech',
        budget: 25000,
        timeline: 'Q2 2024',
        score: 65,
        assignedToId: salesUser.id,
        createdById: user.id,
        companyId: company.id,
        description: 'Looking for cost-effective solution'
      }
    })

    console.log('✅ Created leads:', lead1.firstName + ' ' + lead1.lastName, lead2.firstName + ' ' + lead2.lastName)

    // Convert one lead to customer
    const customer1 = await prisma.customer.create({
      data: {
        name: 'Alice Johnson',
        email: '<EMAIL>',
        phone: '******-1001',
        companyName: 'Potential Corp',
        title: 'CTO',
        website: 'https://potential.com',
        address: '100 Potential St',
        city: 'Tech City',
        state: 'CA',
        country: 'USA',
        postalCode: '90210',
        industry: 'Software',
        status: 'ACTIVE',
        priority: 'HIGH',
        creditLimit: 100000,
        paymentTerms: 'Net 30',
        assignedToId: salesUser.id,
        createdById: user.id,
        companyId: company.id,
        description: 'Converted from lead - enterprise client'
      }
    })

    // Create another customer
    const customer2 = await prisma.customer.create({
      data: {
        name: 'David Brown',
        email: '<EMAIL>',
        phone: '******-2001',
        companyName: 'Enterprise Solutions Ltd',
        title: 'VP of Operations',
        website: 'https://enterprise.com',
        address: '300 Corporate Blvd',
        city: 'New York',
        state: 'NY',
        country: 'USA',
        postalCode: '10001',
        industry: 'Consulting',
        status: 'ACTIVE',
        priority: 'HIGH',
        creditLimit: 200000,
        paymentTerms: 'Net 15',
        assignedToId: salesUser.id,
        createdById: user.id,
        companyId: company.id,
        description: 'Long-term enterprise customer'
      }
    })

    console.log('✅ Created customers:', customer1.name, customer2.name)

    // Create some product/service items
    const item1 = await prisma.item.create({
      data: {
        name: 'Website Development',
        description: 'Complete website development with modern tech stack',
        sku: 'WEB-DEV-001',
        category: 'Development',
        unitPrice: 5000,
        costPrice: 3000,
        taxable: true,
        taxRate: 10,
        active: true,
        companyId: company.id
      }
    })

    const item2 = await prisma.item.create({
      data: {
        name: 'Mobile App Development',
        description: 'iOS and Android mobile application development',
        sku: 'MOB-DEV-001',
        category: 'Development',
        unitPrice: 8000,
        costPrice: 5000,
        taxable: true,
        taxRate: 10,
        active: true,
        companyId: company.id
      }
    })

    const item3 = await prisma.item.create({
      data: {
        name: 'Consulting Services',
        description: 'Technical consulting and advisory services',
        sku: 'CONS-001',
        category: 'Consulting',
        unitPrice: 150, // per hour
        costPrice: 100,
        taxable: true,
        taxRate: 10,
        active: true,
        companyId: company.id
      }
    })

    console.log('✅ Created items:', item1.name, item2.name, item3.name)

    // Create some test quotations
    const quotation1 = await prisma.quotation.create({
      data: {
        quotationNumber: 'QUO-2024-0001',
        title: 'Enterprise Website Development',
        description: 'Complete enterprise website development project with modern tech stack',
        customerId: customer1.id,
        status: 'SENT',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        subtotal: 13000,
        taxRate: 10,
        taxAmount: 1300,
        discountType: 'PERCENTAGE',
        discountValue: 5,
        discountAmount: 650,
        total: 13650,
        terms: 'Payment terms: 50% upfront, 50% on completion',
        paymentTerms: 'Net 30',
        assignedToId: salesUser.id,
        createdById: user.id,
        companyId: company.id,
        items: {
          create: [
            {
              name: 'Website Development',
              description: 'Complete website development with modern tech stack',
              quantity: 1,
              unitPrice: 5000,
              discount: 0,
              taxRate: 10,
              total: 5000,
              itemId: item1.id
            },
            {
              name: 'Mobile App Development',
              description: 'iOS and Android mobile application development',
              quantity: 1,
              unitPrice: 8000,
              discount: 0,
              taxRate: 10,
              total: 8000,
              itemId: item2.id
            }
          ]
        }
      }
    })

    const quotation2 = await prisma.quotation.create({
      data: {
        quotationNumber: 'QUO-2024-0002',
        title: 'Consulting Services Package',
        description: 'Technical consulting and advisory services for Q1 2024',
        customerId: customer2.id,
        status: 'ACCEPTED',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        subtotal: 15000,
        taxRate: 10,
        taxAmount: 1500,
        discountType: 'FIXED',
        discountValue: 1000,
        discountAmount: 1000,
        total: 15500,
        terms: 'Monthly retainer agreement',
        paymentTerms: 'Net 15',
        assignedToId: salesUser.id,
        createdById: user.id,
        companyId: company.id,
        items: {
          create: [
            {
              name: 'Consulting Services',
              description: 'Technical consulting and advisory services (100 hours)',
              quantity: 100,
              unitPrice: 150,
              discount: 0,
              taxRate: 10,
              total: 15000,
              itemId: item3.id
            }
          ]
        }
      }
    })

    console.log('✅ Created quotations:', quotation1.quotationNumber, quotation2.quotationNumber)

    // Create invoices from quotations
    const invoice1 = await prisma.invoice.create({
      data: {
        invoiceNumber: 'INV-2024-0001',
        title: 'Enterprise Website Development Invoice',
        description: 'Invoice for enterprise website development project',
        customerId: customer1.id,
        quotationId: quotation1.id,
        status: 'SENT',
        issueDate: new Date(),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        subtotal: 13000,
        taxRate: 10,
        taxAmount: 1300,
        discountType: 'PERCENTAGE',
        discountValue: 5,
        discountAmount: 650,
        total: 13650,
        paidAmount: 0,
        balanceAmount: 13650,
        terms: 'Payment terms: Net 30 days',
        paymentTerms: 'Net 30',
        paymentMethod: 'Bank Transfer',
        assignedToId: accountantUser.id,
        createdById: user.id,
        companyId: company.id,
        items: {
          create: [
            {
              name: 'Website Development',
              description: 'Complete website development with modern tech stack',
              quantity: 1,
              unitPrice: 5000,
              discount: 0,
              taxRate: 10,
              total: 5000,
              itemId: item1.id
            },
            {
              name: 'Mobile App Development',
              description: 'iOS and Android mobile application development',
              quantity: 1,
              unitPrice: 8000,
              discount: 0,
              taxRate: 10,
              total: 8000,
              itemId: item2.id
            }
          ]
        }
      }
    })

    const invoice2 = await prisma.invoice.create({
      data: {
        invoiceNumber: 'INV-2024-0002',
        title: 'Consulting Services Invoice - January 2024',
        description: 'Monthly consulting services invoice',
        customerId: customer2.id,
        quotationId: quotation2.id,
        status: 'PAID',
        issueDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
        dueDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        paidAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        subtotal: 15000,
        taxRate: 10,
        taxAmount: 1500,
        discountType: 'FIXED',
        discountValue: 1000,
        discountAmount: 1000,
        total: 15500,
        paidAmount: 15500,
        balanceAmount: 0,
        terms: 'Monthly retainer - paid in advance',
        paymentTerms: 'Net 15',
        paymentMethod: 'Credit Card',
        assignedToId: accountantUser.id,
        createdById: user.id,
        companyId: company.id,
        items: {
          create: [
            {
              name: 'Consulting Services',
              description: 'Technical consulting and advisory services (100 hours)',
              quantity: 100,
              unitPrice: 150,
              discount: 0,
              taxRate: 10,
              total: 15000,
              itemId: item3.id
            }
          ]
        }
      }
    })

    console.log('✅ Created invoices:', invoice1.invoiceNumber, invoice2.invoiceNumber)

    // Create contracts
    const contract1 = await prisma.contract.create({
      data: {
        contractNumber: 'CON-2024-0001',
        title: 'Enterprise Website Development Agreement',
        description: 'Service agreement for enterprise website development project',
        type: 'SERVICE',
        customerId: customer1.id,
        quotationId: quotation1.id,
        invoiceId: invoice1.id,
        status: 'SENT',
        priority: 'HIGH',
        startDate: new Date(),
        endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
        value: 13650,
        terms: 'Standard service agreement terms and conditions',
        conditions: 'Project completion within 90 days, regular milestone reviews',
        signatureRequired: true,
        assignedToId: salesUser.id,
        createdById: user.id,
        companyId: company.id
      }
    })

    const contract2 = await prisma.contract.create({
      data: {
        contractNumber: 'CON-2024-0002',
        title: 'Consulting Services Retainer Agreement',
        description: 'Monthly retainer agreement for consulting services',
        type: 'CONSULTING',
        customerId: customer2.id,
        quotationId: quotation2.id,
        status: 'SIGNED',
        priority: 'MEDIUM',
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
        renewalDate: new Date(Date.now() + 335 * 24 * 60 * 60 * 1000), // 30 days before end
        autoRenewal: true,
        renewalPeriod: 12, // 12 months
        signedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        value: 186000, // 12 months * 15500
        terms: 'Monthly retainer agreement with auto-renewal',
        conditions: '100 hours per month, unused hours do not roll over',
        signatureRequired: true,
        assignedToId: salesUser.id,
        createdById: user.id,
        companyId: company.id
      }
    })

    console.log('✅ Created contracts:', contract1.contractNumber, contract2.contractNumber)

    // Create some activities
    await prisma.activity.create({
      data: {
        type: 'NOTE',
        title: 'Initial Contact',
        description: 'First contact with potential client via website form',
        createdById: salesUser.id,
        companyId: company.id,
        leadId: lead1.id
      }
    })

    await prisma.activity.create({
      data: {
        type: 'CALL',
        title: 'Discovery Call',
        description: 'Had a 30-minute discovery call to understand requirements',
        createdById: salesUser.id,
        companyId: company.id,
        customerId: customer1.id
      }
    })

    await prisma.activity.create({
      data: {
        type: 'STATUS_CHANGE',
        title: 'Quotation Sent',
        description: 'Quotation QUO-2024-0001 sent to customer',
        createdById: salesUser.id,
        companyId: company.id,
        quotationId: quotation1.id,
        customerId: customer1.id
      }
    })

    await prisma.activity.create({
      data: {
        type: 'CONTRACT_SIGNED',
        title: 'Contract Signed',
        description: 'Customer signed the consulting services agreement',
        createdById: salesUser.id,
        companyId: company.id,
        contractId: contract2.id,
        customerId: customer2.id
      }
    })

    console.log('✅ Created sample activities')

    // Create some tasks
    await prisma.task.create({
      data: {
        title: 'Follow up with Alice Johnson',
        description: 'Schedule a follow-up call to discuss the quotation',
        status: 'TODO',
        priority: 'HIGH',
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        assignedToId: salesUser.id,
        createdById: user.id,
        companyId: company.id,
        customerId: customer1.id,
        quotationId: quotation1.id
      }
    })

    await prisma.task.create({
      data: {
        title: 'Prepare project kickoff meeting',
        description: 'Organize kickoff meeting for the consulting project',
        status: 'IN_PROGRESS',
        priority: 'MEDIUM',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
        assignedToId: user.id,
        createdById: user.id,
        companyId: company.id,
        customerId: customer2.id,
        contractId: contract2.id
      }
    })

    console.log('✅ Created sample tasks')

    console.log('\n🎉 Enterprise test data setup complete!')
    console.log('\n👥 Login credentials:')
    console.log('📧 Admin: <EMAIL> | 🔑 Password: password123')
    console.log('📧 Sales: <EMAIL> | 🔑 Password: password123')
    console.log('📧 Accountant: <EMAIL> | 🔑 Password: password123')
    console.log('\n📊 Created:')
    console.log(`• 1 Company (${company.name})`)
    console.log('• 3 Users (Admin, Sales, Accountant)')
    console.log('• 2 Leads')
    console.log('• 2 Customers')
    console.log('• 3 Items/Services')
    console.log('• 2 Quotations')
    console.log('• 2 Invoices')
    console.log('• 2 Contracts')
    console.log('• Sample Activities & Tasks')
    console.log('\n🚀 You can now sign in and test the full enterprise application!')

  } catch (error) {
    console.error('❌ Error setting up test data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/leads/route";
exports.ids = ["app/api/leads/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleads%2Froute&page=%2Fapi%2Fleads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleads%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleads%2Froute&page=%2Fapi%2Fleads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleads%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_leads_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/leads/route.ts */ \"(rsc)/./app/api/leads/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/leads/route\",\n        pathname: \"/api/leads\",\n        filename: \"route\",\n        bundlePath: \"app/api/leads/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\leads\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_leads_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/leads/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleads%2Froute&page=%2Fapi%2Fleads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleads%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/leads/route.ts":
/*!********************************!*\
  !*** ./app/api/leads/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for lead creation/update\nconst leadSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Title is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"NEW\",\n        \"CONTACTED\",\n        \"QUALIFIED\",\n        \"PROPOSAL\",\n        \"NEGOTIATION\",\n        \"CLOSED_WON\",\n        \"CLOSED_LOST\"\n    ]).default(\"NEW\"),\n    priority: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"LOW\",\n        \"MEDIUM\",\n        \"HIGH\",\n        \"URGENT\"\n    ]).default(\"MEDIUM\"),\n    source: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    value: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).optional().nullable(),\n    expectedCloseDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    contactName: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    contactEmail: zod__WEBPACK_IMPORTED_MODULE_4__.string().email(\"Invalid email\").optional().nullable(),\n    contactPhone: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    company: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    tags: zod__WEBPACK_IMPORTED_MODULE_4__.array(zod__WEBPACK_IMPORTED_MODULE_4__.string()).optional()\n});\n// GET /api/leads - List leads with filtering and pagination\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const search = searchParams.get(\"search\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const priority = searchParams.get(\"priority\") || \"\";\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            companyId: session.user.companyId || undefined\n        };\n        if (search) {\n            where.OR = [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    contactName: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    contactEmail: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    company: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                }\n            ];\n        }\n        if (status) {\n            where.status = status;\n        }\n        if (priority) {\n            where.priority = priority;\n        }\n        // Get leads with pagination\n        const [leads, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.lead.findMany({\n                where,\n                skip,\n                take: limit,\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    assignedTo: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            activities: true,\n                            quotations: true\n                        }\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.lead.count({\n                where\n            })\n        ]);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            leads,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching leads:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch leads\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/leads - Create new lead\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = leadSchema.parse(body);\n        // Convert expectedCloseDate to Date if provided\n        const leadData = {\n            ...validatedData,\n            tags: validatedData.tags || [],\n            companyId: session.user.companyId,\n            createdById: session.user.id,\n            assignedToId: session.user.id // Default to creator\n        };\n        if (validatedData.expectedCloseDate) {\n            leadData.expectedCloseDate = new Date(validatedData.expectedCloseDate);\n        }\n        const lead = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.lead.create({\n            data: leadData,\n            include: {\n                customer: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true,\n                        company: true\n                    }\n                },\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                assignedTo: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                _count: {\n                    select: {\n                        activities: true,\n                        quotations: true\n                    }\n                }\n            }\n        });\n        // Log activity\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.create({\n            data: {\n                type: \"NOTE\",\n                title: \"Lead Created\",\n                description: `Lead \"${lead.title}\" was created`,\n                leadId: lead.id,\n                customerId: lead.customerId,\n                companyId: session.user.companyId,\n                createdById: session.user.id\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(lead, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error creating lead:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create lead\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/leads/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    },\n                    include: {\n                        company: true,\n                        ownedCompany: true\n                    }\n                });\n                if (!user || !user.password) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                // Update last login\n                await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.update({\n                    where: {\n                        id: user.id\n                    },\n                    data: {\n                        lastLoginAt: new Date(),\n                        loginCount: {\n                            increment: 1\n                        }\n                    }\n                });\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    companyId: user.companyId,\n                    company: user.company || user.ownedCompany\n                };\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.companyId = user.companyId;\n                token.company = user.company;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                session.user.company = token.company;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleads%2Froute&page=%2Fapi%2Fleads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleads%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
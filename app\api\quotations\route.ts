import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for quotation items
const quotationItemSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  discount: z.number().min(0).max(100).optional().default(0),
  taxRate: z.number().min(0).max(100).optional().default(0)
})

// Validation schema for quotation creation/update
const quotationSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional().nullable(),
  customerId: z.string().min(1, 'Customer is required'),
  leadId: z.string().optional().nullable(),
  status: z.enum(['DRAFT', 'SENT', 'VIEWED', 'ACCEPTED', 'REJECTED', 'EXPIRED']).default('DRAFT'),
  validUntil: z.string().optional().nullable(),
  terms: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  items: z.array(quotationItemSchema).min(1, 'At least one item is required'),
  taxRate: z.number().min(0).max(100).optional().default(0),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).optional().default('PERCENTAGE'),
  discountValue: z.number().min(0).optional().default(0)
})

// GET /api/quotations - List quotations with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const customerId = searchParams.get('customerId') || ''
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId || undefined
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { quotationNumber: { contains: search, mode: 'insensitive' } },
        { customer: { name: { contains: search, mode: 'insensitive' } } }
      ]
    }

    if (status) {
      where.status = status
    }

    if (customerId) {
      where.customerId = customerId
    }

    // Get quotations with pagination
    const [quotations, total] = await Promise.all([
      prisma.quotation.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          lead: {
            select: { id: true, name: true, status: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          items: {
            orderBy: { createdAt: 'asc' }
          },
          _count: {
            select: {
              activities: true
            }
          }
        }
      }),
      prisma.quotation.count({ where })
    ])

    // Calculate totals for each quotation
    const quotationsWithTotals = quotations.map(quotation => {
      const subtotal = quotation.items.reduce((sum, item) => {
        const itemTotal = item.quantity * item.unitPrice
        const discountAmount = (itemTotal * item.discount) / 100
        const afterDiscount = itemTotal - discountAmount
        const taxAmount = (afterDiscount * item.taxRate) / 100
        return sum + afterDiscount + taxAmount
      }, 0)

      let total = subtotal
      if (quotation.discountType === 'PERCENTAGE') {
        total = subtotal - (subtotal * quotation.discountValue) / 100
      } else {
        total = subtotal - quotation.discountValue
      }

      const finalTaxAmount = (total * quotation.taxRate) / 100
      const finalTotal = total + finalTaxAmount

      return {
        ...quotation,
        subtotal: Math.round(subtotal * 100) / 100,
        total: Math.round(finalTotal * 100) / 100
      }
    })

    return NextResponse.json({
      quotations: quotationsWithTotals,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching quotations:', error)
    return NextResponse.json(
      { error: 'Failed to fetch quotations' },
      { status: 500 }
    )
  }
}

// POST /api/quotations - Create new quotation
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = quotationSchema.parse(body)

    // Generate quotation number
    const currentYear = new Date().getFullYear()
    const lastQuotation = await prisma.quotation.findFirst({
      where: {
        companyId: session.user.companyId || undefined,
        quotationNumber: {
          startsWith: `QUO-${currentYear}-`
        }
      },
      orderBy: { quotationNumber: 'desc' }
    })

    let nextNumber = 1
    if (lastQuotation) {
      const lastNumber = parseInt(lastQuotation.quotationNumber.split('-')[2])
      nextNumber = lastNumber + 1
    }

    const quotationNumber = `QUO-${currentYear}-${nextNumber.toString().padStart(4, '0')}`

    // Prepare quotation data
    const quotationData: any = {
      ...validatedData,
      quotationNumber,
      companyId: session.user.companyId!,
      createdById: session.user.id
    }

    if (validatedData.validUntil) {
      quotationData.validUntil = new Date(validatedData.validUntil)
    }

    // Create quotation with items in a transaction
    const quotation = await prisma.$transaction(async (tx) => {
      const newQuotation = await tx.quotation.create({
        data: {
          ...quotationData,
          items: {
            create: validatedData.items.map(item => ({
              ...item,
              companyId: session.user.companyId!
            }))
          }
        },
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          lead: {
            select: { id: true, name: true, status: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          items: {
            orderBy: { createdAt: 'asc' }
          }
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'NOTE',
          title: 'Quotation Created',
          description: `Quotation "${newQuotation.title}" (${quotationNumber}) was created`,
          quotationId: newQuotation.id,
          customerId: newQuotation.customerId,
          leadId: newQuotation.leadId,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })

      return newQuotation
    })

    return NextResponse.json(quotation, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating quotation:', error)
    return NextResponse.json(
      { error: 'Failed to create quotation' },
      { status: 500 }
    )
  }
}

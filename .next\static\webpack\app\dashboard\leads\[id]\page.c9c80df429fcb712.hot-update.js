"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leads/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/leads/[id]/page.tsx":
/*!*******************************************!*\
  !*** ./app/dashboard/leads/[id]/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeadDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_leads_lead_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/leads/lead-form */ \"(app-pages-browser)/./components/leads/lead-form.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Building2,CheckCircle,DollarSign,Edit,FileText,Globe,Mail,MessageSquare,Phone,Target,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction LeadDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [lead, setLead] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showEditForm, setShowEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const fetchLead = async ()=>{\n        try {\n            const response = await fetch(\"/api/leads/\".concat(params.id));\n            if (!response.ok) {\n                if (response.status === 404) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Lead not found\");\n                    router.push(\"/dashboard/leads\");\n                    return;\n                }\n                throw new Error(\"Failed to fetch lead\");\n            }\n            const data = await response.json();\n            setLead(data.lead);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to load lead details\");\n            console.error(\"Error fetching lead:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (params.id) {\n            fetchLead();\n        }\n    }, [\n        params.id\n    ]);\n    const handleDelete = async ()=>{\n        if (!lead || !confirm('Are you sure you want to delete \"'.concat(lead.firstName, \" \").concat(lead.lastName, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/leads/\".concat(lead.id), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to delete lead\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Lead deleted successfully\");\n            router.push(\"/dashboard/leads\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__.toast.error(error instanceof Error ? error.message : \"Failed to delete lead\");\n        }\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"NEW\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    children: \"New\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 16\n                }, this);\n            case \"CONTACTED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"Contacted\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 16\n                }, this);\n            case \"QUALIFIED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"Qualified\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 16\n                }, this);\n            case \"PROPOSAL\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"Proposal\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 16\n                }, this);\n            case \"NEGOTIATION\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-orange-100 text-orange-800\",\n                    children: \"Negotiation\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 16\n                }, this);\n            case \"CLOSED_WON\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"Closed Won\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 16\n                }, this);\n            case \"CLOSED_LOST\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"destructive\",\n                    children: \"Closed Lost\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getPriorityBadge = (priority)=>{\n        switch(priority){\n            case \"LOW\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    className: \"text-xs\",\n                    children: \"Low\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 16\n                }, this);\n            case \"MEDIUM\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-blue-100 text-blue-800 text-xs\",\n                    children: \"Medium\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 16\n                }, this);\n            case \"HIGH\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-orange-100 text-orange-800 text-xs\",\n                    children: \"High\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 16\n                }, this);\n            case \"URGENT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"destructive\",\n                    className: \"text-xs\",\n                    children: \"Urgent\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    className: \"text-xs\",\n                    children: priority\n                }, void 0, false, {\n                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this);\n    }\n    if (!lead) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Lead Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mb-4\",\n                        children: \"The lead you're looking for doesn't exist.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                            href: \"/dashboard/leads\",\n                            children: \"Back to Leads\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                    href: \"/dashboard/leads\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Leads\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: [\n                                            lead.firstName,\n                                            \" \",\n                                            lead.lastName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: lead.title && lead.companyName ? \"\".concat(lead.title, \" at \").concat(lead.companyName) : lead.title || lead.companyName || \"Lead Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>setShowEditForm(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"destructive\",\n                                onClick: handleDelete,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            getStatusBadge(lead.status)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Priority\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            getPriorityBadge(lead.priority)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: [\n                                                    lead.score,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Budget\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: lead.budget ? \"$\".concat(lead.budget.toLocaleString()) : \"Not specified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                value: activeTab,\n                onValueChange: setActiveTab,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"overview\",\n                                children: \"Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"activities\",\n                                children: [\n                                    \"Activities (\",\n                                    lead._count.activities,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"notes\",\n                                children: [\n                                    \"Notes (\",\n                                    lead._count.leadNotes,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"tasks\",\n                                children: [\n                                    \"Tasks (\",\n                                    lead._count.tasks,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"documents\",\n                                children: [\n                                    \"Documents (\",\n                                    lead._count.documents,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Contact Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: lead.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this),\n                                                lead.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Phone\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: lead.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, this),\n                                                lead.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Website\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: lead.website,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"font-medium text-blue-600 hover:underline\",\n                                                                    children: lead.website\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Company Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                lead.companyName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Company\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: lead.companyName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, this),\n                                                lead.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Job Title\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: lead.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                lead.industry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Industry\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: lead.industry\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this),\n                                                lead.companySize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Company Size\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: lead.companySize\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"activities\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"Activity Timeline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Activity timeline will be implemented next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"notes\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"Lead Notes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Notes management will be implemented next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"tasks\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"Related Tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Task management will be implemented next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"documents\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"Documents\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Building2_CheckCircle_DollarSign_Edit_FileText_Globe_Mail_MessageSquare_Phone_Target_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Document management will be implemented next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, this),\n            showEditForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leads_lead_form__WEBPACK_IMPORTED_MODULE_8__.LeadForm, {\n                isOpen: showEditForm,\n                onClose: ()=>setShowEditForm(false),\n                onSuccess: ()=>{\n                    setShowEditForm(false);\n                    fetchLead();\n                },\n                lead: lead,\n                mode: \"edit\"\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\leads\\\\[id]\\\\page.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(LeadDetailPage, \"Wyn3U8y+rCowsEfTjyUW2mkRKwA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession\n    ];\n});\n_c = LeadDetailPage;\nvar _c;\n$RefreshReg$(_c, \"LeadDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/leads/[id]/page.tsx\n"));

/***/ })

});
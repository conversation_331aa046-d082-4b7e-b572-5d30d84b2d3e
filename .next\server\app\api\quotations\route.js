"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/quotations/route";
exports.ids = ["app/api/quotations/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2Froute&page=%2Fapi%2Fquotations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2Froute&page=%2Fapi%2Fquotations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_quotations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/quotations/route.ts */ \"(rsc)/./app/api/quotations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/quotations/route\",\n        pathname: \"/api/quotations\",\n        filename: \"route\",\n        bundlePath: \"app/api/quotations/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\quotations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_quotations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/quotations/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2Froute&page=%2Fapi%2Fquotations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/quotations/route.ts":
/*!*************************************!*\
  !*** ./app/api/quotations/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for quotation items\nconst quotationItemSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Description is required\"),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(1, \"Quantity must be at least 1\"),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Unit price must be positive\"),\n    discount: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0)\n});\n// Validation schema for quotation creation/update\nconst quotationSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Title is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Customer is required\"),\n    leadId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"DRAFT\",\n        \"SENT\",\n        \"VIEWED\",\n        \"ACCEPTED\",\n        \"REJECTED\",\n        \"EXPIRED\"\n    ]).default(\"DRAFT\"),\n    validUntil: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    items: zod__WEBPACK_IMPORTED_MODULE_4__.array(quotationItemSchema).min(1, \"At least one item is required\"),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0),\n    discountType: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"PERCENTAGE\",\n        \"FIXED\"\n    ]).optional().default(\"PERCENTAGE\"),\n    discountValue: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).optional().default(0)\n});\n// GET /api/quotations - List quotations with filtering and pagination\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const search = searchParams.get(\"search\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const customerId = searchParams.get(\"customerId\") || \"\";\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            companyId: session.user.companyId || undefined\n        };\n        if (search) {\n            where.OR = [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    quotationNumber: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    customer: {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ];\n        }\n        if (status) {\n            where.status = status;\n        }\n        if (customerId) {\n            where.customerId = customerId;\n        }\n        // Get quotations with pagination\n        const [quotations, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.findMany({\n                where,\n                skip,\n                take: limit,\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    lead: {\n                        select: {\n                            id: true,\n                            title: true,\n                            status: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: {\n                        orderBy: {\n                            createdAt: \"asc\"\n                        }\n                    },\n                    _count: {\n                        select: {\n                            activities: true\n                        }\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.count({\n                where\n            })\n        ]);\n        // Calculate totals for each quotation\n        const quotationsWithTotals = quotations.map((quotation)=>{\n            const subtotal = quotation.items.reduce((sum, item)=>{\n                const itemTotal = item.quantity * item.unitPrice;\n                const discountAmount = itemTotal * item.discount / 100;\n                const afterDiscount = itemTotal - discountAmount;\n                const taxAmount = afterDiscount * item.taxRate / 100;\n                return sum + afterDiscount + taxAmount;\n            }, 0);\n            let total = subtotal;\n            if (quotation.discountType === \"PERCENTAGE\") {\n                total = subtotal - subtotal * quotation.discountValue / 100;\n            } else {\n                total = subtotal - quotation.discountValue;\n            }\n            const finalTaxAmount = total * quotation.taxRate / 100;\n            const finalTotal = total + finalTaxAmount;\n            return {\n                ...quotation,\n                subtotal: Math.round(subtotal * 100) / 100,\n                total: Math.round(finalTotal * 100) / 100\n            };\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            quotations: quotationsWithTotals,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching quotations:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch quotations\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/quotations - Create new quotation\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = quotationSchema.parse(body);\n        // Generate quotation number\n        const currentYear = new Date().getFullYear();\n        const lastQuotation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.findFirst({\n            where: {\n                companyId: session.user.companyId || undefined,\n                quotationNumber: {\n                    startsWith: `QUO-${currentYear}-`\n                }\n            },\n            orderBy: {\n                quotationNumber: \"desc\"\n            }\n        });\n        let nextNumber = 1;\n        if (lastQuotation) {\n            const lastNumber = parseInt(lastQuotation.quotationNumber.split(\"-\")[2]);\n            nextNumber = lastNumber + 1;\n        }\n        const quotationNumber = `QUO-${currentYear}-${nextNumber.toString().padStart(4, \"0\")}`;\n        // Prepare quotation data\n        const quotationData = {\n            ...validatedData,\n            quotationNumber,\n            companyId: session.user.companyId,\n            createdById: session.user.id\n        };\n        if (validatedData.validUntil) {\n            quotationData.validUntil = new Date(validatedData.validUntil);\n        }\n        // Create quotation with items in a transaction\n        const quotation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            const newQuotation = await tx.quotation.create({\n                data: {\n                    ...quotationData,\n                    items: {\n                        create: validatedData.items.map((item)=>({\n                                ...item,\n                                companyId: session.user.companyId\n                            }))\n                    }\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    lead: {\n                        select: {\n                            id: true,\n                            title: true,\n                            status: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: {\n                        orderBy: {\n                            createdAt: \"asc\"\n                        }\n                    }\n                }\n            });\n            // Log activity\n            await tx.activity.create({\n                data: {\n                    type: \"NOTE\",\n                    title: \"Quotation Created\",\n                    description: `Quotation \"${newQuotation.title}\" (${quotationNumber}) was created`,\n                    quotationId: newQuotation.id,\n                    customerId: newQuotation.customerId,\n                    leadId: newQuotation.leadId,\n                    companyId: session.user.companyId,\n                    createdById: session.user.id\n                }\n            });\n            return newQuotation;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(quotation, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error creating quotation:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create quotation\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/quotations/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    },\n                    include: {\n                        company: true,\n                        ownedCompany: true\n                    }\n                });\n                if (!user || !user.password) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                // Update last login\n                await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.update({\n                    where: {\n                        id: user.id\n                    },\n                    data: {\n                        lastLoginAt: new Date(),\n                        loginCount: {\n                            increment: 1\n                        }\n                    }\n                });\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    companyId: user.companyId,\n                    company: user.company || user.ownedCompany\n                };\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.companyId = user.companyId;\n                token.company = user.company;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                session.user.company = token.company;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2Froute&page=%2Fapi%2Fquotations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
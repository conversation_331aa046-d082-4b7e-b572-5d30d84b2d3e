"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    var _session_user, _session_user1, _session_user2, _session_user3;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        var _session_user;\n        const fetchDashboardData = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/dashboard\");\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch dashboard data\");\n                }\n                const data = await response.json();\n                setDashboardData(data);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"An error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.companyId) {\n            fetchDashboardData();\n        }\n    }, [\n        session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.companyId\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 77,\n            columnNumber: 12\n        }, this);\n    }\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Error Loading Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: error || \"Failed to load dashboard data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        className: \"mt-4\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    const stats = [\n        {\n            title: \"Total Customers\",\n            value: dashboardData.stats.customers.total.toLocaleString(),\n            change: \"\".concat(dashboardData.stats.customers.change >= 0 ? \"+\" : \"\").concat(dashboardData.stats.customers.change.toFixed(1), \"%\"),\n            changeType: dashboardData.stats.customers.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"text-blue-600\",\n            subtitle: \"\".concat(dashboardData.stats.customers.current, \" this month\")\n        },\n        {\n            title: \"Active Leads\",\n            value: dashboardData.stats.leads.current.toLocaleString(),\n            change: \"\".concat(dashboardData.stats.leads.change >= 0 ? \"+\" : \"\").concat(dashboardData.stats.leads.change.toFixed(1), \"%\"),\n            changeType: dashboardData.stats.leads.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"text-green-600\",\n            subtitle: \"\".concat(dashboardData.stats.leads.total, \" total leads\")\n        },\n        {\n            title: \"Quotations\",\n            value: dashboardData.stats.quotations.current.toLocaleString(),\n            change: \"\".concat(dashboardData.stats.quotations.change >= 0 ? \"+\" : \"\").concat(dashboardData.stats.quotations.change.toFixed(1), \"%\"),\n            changeType: dashboardData.stats.quotations.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"text-yellow-600\",\n            subtitle: \"\".concat(dashboardData.stats.quotations.total, \" total\")\n        },\n        {\n            title: \"Revenue\",\n            value: \"$\".concat(dashboardData.stats.revenue.total.toLocaleString()),\n            change: \"\".concat(dashboardData.stats.revenue.change >= 0 ? \"+\" : \"\").concat(dashboardData.stats.revenue.change.toFixed(1), \"%\"),\n            changeType: dashboardData.stats.revenue.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"text-purple-600\",\n            subtitle: \"$\".concat(dashboardData.stats.revenue.pending.toLocaleString(), \" pending\")\n        },\n        {\n            title: \"Active Contracts\",\n            value: dashboardData.stats.contracts.current.toLocaleString(),\n            change: \"\",\n            changeType: \"neutral\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            color: \"text-indigo-600\",\n            subtitle: \"\".concat(dashboardData.stats.contracts.total, \" total\")\n        },\n        {\n            title: \"Open Tasks\",\n            value: dashboardData.stats.tasks.current.toLocaleString(),\n            change: \"\",\n            changeType: \"neutral\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            color: \"text-orange-600\",\n            subtitle: \"\".concat(dashboardData.stats.tasks.total, \" total tasks\")\n        }\n    ];\n    // Process recent activities\n    const processedActivities = dashboardData.recentActivities.map((activity)=>{\n        var _activity_createdBy;\n        let message = activity.description;\n        let icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n        let status = \"info\";\n        // Customize based on activity type\n        switch(activity.type){\n            case \"NOTE\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n                status = \"info\";\n                break;\n            case \"CALL\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n                status = \"success\";\n                break;\n            case \"EMAIL\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"];\n                status = \"info\";\n                break;\n            case \"MEETING\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"];\n                status = \"warning\";\n                break;\n            case \"STATUS_CHANGE\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n                status = \"success\";\n                break;\n            case \"PAYMENT_RECEIVED\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n                status = \"success\";\n                break;\n            case \"CONTRACT_SIGNED\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n                status = \"success\";\n                break;\n            default:\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n                status = \"info\";\n        }\n        return {\n            id: activity.id,\n            type: activity.type,\n            message: activity.title,\n            description: activity.description,\n            time: new Date(activity.createdAt).toLocaleString(),\n            icon,\n            status,\n            user: ((_activity_createdBy = activity.createdBy) === null || _activity_createdBy === void 0 ? void 0 : _activity_createdBy.name) || \"System\"\n        };\n    });\n    const quickActions = [\n        {\n            title: \"Add Lead\",\n            description: \"Track a new business lead\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            href: \"/dashboard/leads/new\",\n            color: \"text-green-600\"\n        },\n        {\n            title: \"Add Customer\",\n            description: \"Create a new customer profile\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/dashboard/customers/new\",\n            color: \"text-blue-600\"\n        },\n        {\n            title: \"Create Quotation\",\n            description: \"Generate a new quotation\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            href: \"/dashboard/quotations/new\",\n            color: \"text-yellow-600\"\n        },\n        {\n            title: \"Create Invoice\",\n            description: \"Generate a new invoice\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            href: \"/dashboard/invoices/new\",\n            color: \"text-purple-600\"\n        },\n        {\n            title: \"New Contract\",\n            description: \"Create a new contract\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            href: \"/dashboard/contracts/new\",\n            color: \"text-indigo-600\"\n        },\n        {\n            title: \"View Reports\",\n            description: \"Analyze your business data\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            href: \"/dashboard/reports\",\n            color: \"text-orange-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: [\n                            \"Welcome back, \",\n                            (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.name) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.email) || \"User\",\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-100\",\n                        children: \"Here's what's happening with your business today.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center space-x-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"mr-2\",\n                                        children: (session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.role) || \"USER\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Role\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"mr-2 text-white border-white\",\n                                        children: [\n                                            dashboardData.stats.customers.total,\n                                            \" Customers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"mr-2 text-white border-white\",\n                                        children: [\n                                            dashboardData.stats.leads.current,\n                                            \" Active Leads\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\",\n                children: stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"hover:shadow-md transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: stat.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-5 w-5 \".concat(stat.color)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this),\n                                    stat.change && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-1\",\n                                        children: [\n                                            stat.changeType === \"increase\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-600 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 21\n                                            }, this) : stat.changeType === \"decrease\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-600 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium \".concat(stat.changeType === \"increase\" ? \"text-green-600\" : stat.changeType === \"decrease\" ? \"text-red-600\" : \"text-gray-500\"),\n                                                children: stat.change\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this),\n                                            stat.changeType !== \"neutral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 ml-1\",\n                                                children: \"from last month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this),\n                                    stat.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: stat.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, stat.title, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Quick Actions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3\",\n                                    children: quickActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            className: \"h-auto p-3 flex flex-col items-center space-y-2 hover:bg-gray-50\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: action.href,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                        className: \"h-5 w-5 \".concat(action.color)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-xs\",\n                                                                children: action.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: action.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, action.title, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Recent Activity\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 max-h-80 overflow-y-auto\",\n                                    children: processedActivities.length > 0 ? processedActivities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(activity.status === \"success\" ? \"bg-green-100\" : activity.status === \"warning\" ? \"bg-yellow-100\" : \"bg-blue-100\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(activity.icon, {\n                                                        className: \"h-4 w-4 \".concat(activity.status === \"success\" ? \"text-green-600\" : activity.status === \"warning\" ? \"text-yellow-600\" : \"text-blue-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: activity.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: activity.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-1 text-xs text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: activity.user\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mx-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: activity.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No recent activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upcoming Tasks\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: dashboardData.upcomingTasks.length > 0 ? dashboardData.upcomingTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(task.priority === \"HIGH\" ? \"bg-red-100\" : task.priority === \"MEDIUM\" ? \"bg-yellow-100\" : \"bg-green-100\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(task.priority === \"HIGH\" ? \"text-red-600\" : task.priority === \"MEDIUM\" ? \"text-yellow-600\" : \"text-green-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Due: \",\n                                                                new Date(task.dueDate).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        task.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                \"Assigned to: \",\n                                                                task.assignedTo.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No upcoming tasks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileContract, {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upcoming Renewals\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: dashboardData.upcomingRenewals.length > 0 ? dashboardData.upcomingRenewals.map((contract)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full bg-orange-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: contract.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Customer: \",\n                                                                contract.customer.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Renewal: \",\n                                                                new Date(contract.renewalDate).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, contract.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No upcoming renewals\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"Je5vi3T2RG38wD3MkwriTKJHO44=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession\n    ];\n});\n_c = DashboardPage;\n// Loading skeleton component\nfunction DashboardSkeleton() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-gray-300 to-gray-400 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                        className: \"h-8 w-64 mb-2 bg-gray-200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                        className: \"h-4 w-96 bg-gray-200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                className: \"h-6 w-20 bg-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                className: \"h-6 w-32 bg-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 492,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\",\n                children: Array.from({\n                    length: 6\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-4 w-24\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-8 w-16 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-4 w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-6 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3\",\n                                    children: Array.from({\n                                        length: 6\n                                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-20\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-6 w-40\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: Array.from({\n                                        length: 5\n                                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                    className: \"h-10 w-10 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                            className: \"h-4 w-full mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                            className: \"h-3 w-24\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, i, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 490,\n        columnNumber: 5\n    }, this);\n}\n_c1 = DashboardSkeleton;\nvar _c, _c1;\n$RefreshReg$(_c, \"DashboardPage\");\n$RefreshReg$(_c1, \"DashboardSkeleton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});
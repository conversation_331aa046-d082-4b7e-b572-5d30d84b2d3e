'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { ColumnDef } from '@tanstack/react-table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { LeadForm } from '@/components/leads/lead-form'
import {
  UserPlus,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Mail,
  Phone,
  Building2,
  DollarSign,
  Calendar,
  TrendingUp,
  Target,
  Clock
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface Lead {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string | null
  companyName: string | null
  title: string | null
  website: string | null
  source: 'WEBSITE' | 'REFERRAL' | 'SOCIAL_MEDIA' | 'EMAIL_CAMPAIGN' | 'COLD_CALL' | 'TRADE_SHOW' | 'PARTNER' | 'OTHER'
  status: 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'PROPOSAL' | 'NEGOTIATION' | 'CLOSED_WON' | 'CLOSED_LOST'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  address: string | null
  city: string | null
  state: string | null
  country: string | null
  postalCode: string | null
  industry: string | null
  companySize: string | null
  budget: number | null
  timeline: string | null
  score: number
  qualified: boolean
  qualifiedAt: string | null
  convertedAt: string | null
  customerId: string | null
  customer: {
    id: string
    name: string
    companyName: string | null
  } | null
  assignedToId: string | null
  assignedTo: {
    id: string
    name: string | null
    email: string
  } | null
  description: string | null
  createdAt: string
  updatedAt: string
  _count: {
    activities: number
    leadNotes: number
    tasks: number
    documents: number
  }
}

export default function LeadsPage() {
  const { data: session } = useSession()
  const [leads, setLeads] = useState<Lead[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingLead, setEditingLead] = useState<Lead | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    new: 0,
    qualified: 0,
    closedWon: 0,
    totalValue: 0
  })

  const fetchLeads = async () => {
    try {
      const response = await fetch('/api/leads')
      if (!response.ok) throw new Error('Failed to fetch leads')

      const data = await response.json()
      setLeads(data.leads)

      // Calculate stats
      const total = data.leads.length
      const newLeads = data.leads.filter((l: Lead) => l.status === 'NEW').length
      const qualified = data.leads.filter((l: Lead) => l.status === 'QUALIFIED').length
      const closedWon = data.leads.filter((l: Lead) => l.status === 'CLOSED_WON').length
      const totalValue = data.leads.reduce((sum: number, l: Lead) => sum + (l.budget || 0), 0)

      setStats({ total, new: newLeads, qualified, closedWon, totalValue })
    } catch (error) {
      toast.error('Failed to load leads')
      console.error('Error fetching leads:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchLeads()
  }, [])

  const handleDelete = async (lead: Lead) => {
    if (!confirm(`Are you sure you want to delete "${lead.firstName} ${lead.lastName}"?`)) return

    try {
      const response = await fetch(`/api/leads/${lead.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete lead')
      }

      toast.success('Lead deleted successfully')
      fetchLeads()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete lead')
    }
  }

  const handleEdit = (lead: Lead) => {
    setEditingLead(lead)
    setShowForm(true)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingLead(null)
  }

  const getStatusBadge = (status: string | null | undefined) => {
    if (!status) return <Badge variant="secondary">Unknown</Badge>

    switch (status) {
      case 'NEW':
        return <Badge variant="secondary">New</Badge>
      case 'CONTACTED':
        return <Badge variant="info">Contacted</Badge>
      case 'QUALIFIED':
        return <Badge variant="success">Qualified</Badge>
      case 'PROPOSAL':
        return <Badge variant="warning">Proposal</Badge>
      case 'NEGOTIATION':
        return <Badge variant="warning">Negotiation</Badge>
      case 'CLOSED_WON':
        return <Badge variant="success">Closed Won</Badge>
      case 'CLOSED_LOST':
        return <Badge variant="destructive">Closed Lost</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string | null | undefined) => {
    if (!priority) return <Badge variant="secondary" className="text-xs">Unknown</Badge>

    switch (priority) {
      case 'LOW':
        return <Badge variant="secondary" className="text-xs">Low</Badge>
      case 'MEDIUM':
        return <Badge variant="info" className="text-xs">Medium</Badge>
      case 'HIGH':
        return <Badge variant="warning" className="text-xs">High</Badge>
      case 'URGENT':
        return <Badge variant="destructive" className="text-xs">Urgent</Badge>
      default:
        return <Badge variant="secondary" className="text-xs">{priority}</Badge>
    }
  }

  const columns: ColumnDef<Lead>[] = [
    {
      accessorKey: 'firstName',
      header: 'Lead',
      cell: ({ row }) => {
        const lead = row.original
        return (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <TrendingUp className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <div className="font-medium">{lead.firstName} {lead.lastName}</div>
              {lead.companyName && (
                <div className="text-sm text-gray-500">{lead.companyName}</div>
              )}
              {lead.title && (
                <div className="text-xs text-gray-400">{lead.title}</div>
              )}
            </div>
          </div>
        )
      }
    },
    {
      accessorKey: 'email',
      header: 'Contact',
      cell: ({ row }) => {
        const lead = row.original
        return (
          <div className="space-y-1">
            <div className="flex items-center space-x-2 text-sm">
              <Mail className="h-3 w-3 text-gray-400" />
              <span>{lead.email}</span>
            </div>
            {lead.phone && (
              <div className="flex items-center space-x-2 text-sm">
                <Phone className="h-3 w-3 text-gray-400" />
                <span>{lead.phone}</span>
              </div>
            )}
          </div>
        )
      }
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => getStatusBadge(row.getValue('status'))
    },
    {
      accessorKey: 'priority',
      header: 'Priority',
      cell: ({ row }) => getPriorityBadge(row.getValue('priority'))
    },
    {
      accessorKey: 'budget',
      header: 'Budget',
      cell: ({ row }) => {
        const budget = row.getValue('budget') as number
        return budget ? (
          <div className="flex items-center space-x-2">
            <DollarSign className="h-3 w-3 text-green-600" />
            <span className="font-medium">${budget.toLocaleString()}</span>
          </div>
        ) : (
          <span className="text-gray-400 text-sm">-</span>
        )
      }
    },
    {
      accessorKey: 'score',
      header: 'Score',
      cell: ({ row }) => {
        const score = row.getValue('score') as number
        return (
          <div className="flex items-center space-x-2">
            <Target className="h-3 w-3 text-blue-600" />
            <span className="font-medium">{score}/100</span>
          </div>
        )
      }
    },
    {
      accessorKey: 'source',
      header: 'Source',
      cell: ({ row }) => {
        const source = row.getValue('source') as string
        return source ? (
          <Badge variant="outline" className="text-xs">
            {source}
          </Badge>
        ) : (
          <span className="text-gray-400 text-sm">-</span>
        )
      }
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const lead = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/leads/${lead.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEdit(lead)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDelete(lead)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      }
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Leads</h1>
          <p className="text-gray-600 mt-1">Track and convert your business leads</p>
        </div>
        <Button
          className="flex items-center space-x-2"
          onClick={() => setShowForm(true)}
        >
          <Plus className="h-4 w-4" />
          <span>Add Lead</span>
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">All leads</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New</CardTitle>
            <Target className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.new}</div>
            <p className="text-xs text-muted-foreground">New leads</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Qualified</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.qualified}</div>
            <p className="text-xs text-muted-foreground">Qualified leads</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Closed Won</CardTitle>
            <Clock className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.closedWon}</div>
            <p className="text-xs text-muted-foreground">Successful conversions</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pipeline Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total estimated value</p>
          </CardContent>
        </Card>
      </div>

      {/* Leads Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lead Pipeline</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={leads}
              searchPlaceholder="Search leads..."
            />
          )}
        </CardContent>
      </Card>

      {/* Lead Form Modal */}
      <LeadForm
        isOpen={showForm}
        onClose={handleFormClose}
        onSuccess={fetchLeads}
        lead={editingLead}
        mode={editingLead ? 'edit' : 'create'}
      />
    </div>
  )
}

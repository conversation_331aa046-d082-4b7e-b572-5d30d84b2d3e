"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/invoices/route";
exports.ids = ["app/api/invoices/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_invoices_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/invoices/route.ts */ \"(rsc)/./app/api/invoices/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/invoices/route\",\n        pathname: \"/api/invoices\",\n        filename: \"route\",\n        bundlePath: \"app/api/invoices/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\invoices\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_invoices_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/invoices/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/invoices/route.ts":
/*!***********************************!*\
  !*** ./app/api/invoices/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for invoice items\nconst invoiceItemSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Name is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(1, \"Quantity must be at least 1\"),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Unit price must be positive\"),\n    itemId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional()\n});\n// Validation schema for invoice creation/update\nconst invoiceSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    invoiceNumber: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Customer is required\"),\n    quotationId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"DRAFT\",\n        \"SENT\",\n        \"PAID\",\n        \"OVERDUE\",\n        \"CANCELLED\"\n    ]).default(\"DRAFT\"),\n    dueDate: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    items: zod__WEBPACK_IMPORTED_MODULE_4__.array(invoiceItemSchema).min(1, \"At least one item is required\"),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0)\n});\n// GET /api/invoices - List invoices with filtering and pagination\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const search = searchParams.get(\"search\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const customerId = searchParams.get(\"customerId\") || \"\";\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            companyId: session.user.companyId || undefined\n        };\n        if (search) {\n            where.OR = [\n                {\n                    invoiceNumber: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    customer: {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                },\n                {\n                    customer: {\n                        company: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ];\n        }\n        if (status) {\n            where.status = status;\n        }\n        if (customerId) {\n            where.customerId = customerId;\n        }\n        // Get invoices with pagination\n        const [invoices, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findMany({\n                where,\n                skip,\n                take: limit,\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: true,\n                    transactions: true\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count({\n                where\n            })\n        ]);\n        // Return invoices with existing totals from database\n        const invoicesWithTotals = invoices.map((invoice)=>({\n                ...invoice,\n                subtotal: Number(invoice.subtotal),\n                total: Number(invoice.total),\n                taxAmount: Number(invoice.taxAmount),\n                paidAmount: Number(invoice.paidAmount)\n            }));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            invoices: invoicesWithTotals,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching invoices:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch invoices\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/invoices - Create new invoice\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = invoiceSchema.parse(body);\n        // Generate invoice number if not provided\n        let invoiceNumber = validatedData.invoiceNumber;\n        if (!invoiceNumber) {\n            const currentYear = new Date().getFullYear();\n            const lastInvoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findFirst({\n                where: {\n                    companyId: session.user.companyId || undefined,\n                    invoiceNumber: {\n                        startsWith: `INV-${currentYear}-`\n                    }\n                },\n                orderBy: {\n                    invoiceNumber: \"desc\"\n                }\n            });\n            let nextNumber = 1;\n            if (lastInvoice) {\n                const lastNumber = parseInt(lastInvoice.invoiceNumber.split(\"-\")[2]);\n                nextNumber = lastNumber + 1;\n            }\n            invoiceNumber = `INV-${currentYear}-${nextNumber.toString().padStart(4, \"0\")}`;\n        }\n        // Calculate totals\n        const subtotal = validatedData.items.reduce((sum, item)=>{\n            return sum + item.quantity * item.unitPrice;\n        }, 0);\n        const taxAmount = subtotal * (validatedData.taxRate || 0) / 100;\n        const total = subtotal + taxAmount;\n        // Prepare invoice data\n        const invoiceData = {\n            invoiceNumber,\n            title: validatedData.title || `Invoice ${invoiceNumber}`,\n            customerId: validatedData.customerId,\n            quotationId: validatedData.quotationId,\n            status: validatedData.status,\n            subtotal,\n            taxRate: validatedData.taxRate || 0,\n            taxAmount,\n            total,\n            terms: validatedData.terms,\n            notes: validatedData.notes,\n            companyId: session.user.companyId,\n            createdById: session.user.id\n        };\n        if (validatedData.dueDate) {\n            invoiceData.dueDate = new Date(validatedData.dueDate);\n        } else {\n            // Default to 30 days from now\n            const dueDate = new Date();\n            dueDate.setDate(dueDate.getDate() + 30);\n            invoiceData.dueDate = dueDate;\n        }\n        // Create invoice with items in a transaction\n        const invoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            const newInvoice = await tx.invoice.create({\n                data: {\n                    ...invoiceData,\n                    items: {\n                        create: validatedData.items.map((item)=>({\n                                name: item.name,\n                                description: item.description,\n                                quantity: item.quantity,\n                                unitPrice: item.unitPrice,\n                                total: item.quantity * item.unitPrice,\n                                itemId: item.itemId\n                            }))\n                    }\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    quotation: {\n                        select: {\n                            id: true,\n                            quotationNumber: true,\n                            title: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: true\n                }\n            });\n            // If created from quotation, update quotation status\n            if (newInvoice.quotationId) {\n                await tx.quotation.update({\n                    where: {\n                        id: newInvoice.quotationId\n                    },\n                    data: {\n                        status: \"ACCEPTED\"\n                    }\n                });\n            }\n            return newInvoice;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(invoice, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error creating invoice:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create invoice\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/invoices/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    },\n                    include: {\n                        company: true,\n                        ownedCompany: true\n                    }\n                });\n                if (!user || !user.password) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                // Update last login\n                await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.update({\n                    where: {\n                        id: user.id\n                    },\n                    data: {\n                        lastLoginAt: new Date(),\n                        loginCount: {\n                            increment: 1\n                        }\n                    }\n                });\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    companyId: user.companyId,\n                    company: user.company || user.ownedCompany\n                };\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.companyId = user.companyId;\n                token.company = user.company;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                session.user.company = token.company;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
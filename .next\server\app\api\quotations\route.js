"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/quotations/route";
exports.ids = ["app/api/quotations/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2Froute&page=%2Fapi%2Fquotations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2Froute&page=%2Fapi%2Fquotations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_quotations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/quotations/route.ts */ \"(rsc)/./app/api/quotations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/quotations/route\",\n        pathname: \"/api/quotations\",\n        filename: \"route\",\n        bundlePath: \"app/api/quotations/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\quotations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_quotations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/quotations/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2Froute&page=%2Fapi%2Fquotations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/quotations/route.ts":
/*!*************************************!*\
  !*** ./app/api/quotations/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for quotation items\nconst quotationItemSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Description is required\"),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(1, \"Quantity must be at least 1\"),\n    unitPrice: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0, \"Unit price must be positive\"),\n    discount: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0)\n});\n// Validation schema for quotation creation/update\nconst quotationSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Title is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    customerId: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Customer is required\"),\n    leadId: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"DRAFT\",\n        \"SENT\",\n        \"VIEWED\",\n        \"ACCEPTED\",\n        \"REJECTED\",\n        \"EXPIRED\"\n    ]).default(\"DRAFT\"),\n    validUntil: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    items: zod__WEBPACK_IMPORTED_MODULE_4__.array(quotationItemSchema).min(1, \"At least one item is required\"),\n    taxRate: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).max(100).optional().default(0),\n    discountType: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"PERCENTAGE\",\n        \"FIXED\"\n    ]).optional().default(\"PERCENTAGE\"),\n    discountValue: zod__WEBPACK_IMPORTED_MODULE_4__.number().min(0).optional().default(0)\n});\n// GET /api/quotations - List quotations with filtering and pagination\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const search = searchParams.get(\"search\") || \"\";\n        const status = searchParams.get(\"status\") || \"\";\n        const customerId = searchParams.get(\"customerId\") || \"\";\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            companyId: session.user.companyId || undefined\n        };\n        if (search) {\n            where.OR = [\n                {\n                    title: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    quotationNumber: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    customer: {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ];\n        }\n        if (status) {\n            where.status = status;\n        }\n        if (customerId) {\n            where.customerId = customerId;\n        }\n        // Get quotations with pagination\n        const [quotations, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.findMany({\n                where,\n                skip,\n                take: limit,\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    lead: {\n                        select: {\n                            id: true,\n                            name: true,\n                            status: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: {\n                        orderBy: {\n                            createdAt: \"asc\"\n                        }\n                    },\n                    _count: {\n                        select: {\n                            activities: true\n                        }\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.count({\n                where\n            })\n        ]);\n        // Calculate totals for each quotation\n        const quotationsWithTotals = quotations.map((quotation)=>{\n            const subtotal = quotation.items.reduce((sum, item)=>{\n                const itemTotal = item.quantity * item.unitPrice;\n                const discountAmount = itemTotal * item.discount / 100;\n                const afterDiscount = itemTotal - discountAmount;\n                const taxAmount = afterDiscount * item.taxRate / 100;\n                return sum + afterDiscount + taxAmount;\n            }, 0);\n            let total = subtotal;\n            if (quotation.discountType === \"PERCENTAGE\") {\n                total = subtotal - subtotal * quotation.discountValue / 100;\n            } else {\n                total = subtotal - quotation.discountValue;\n            }\n            const finalTaxAmount = total * quotation.taxRate / 100;\n            const finalTotal = total + finalTaxAmount;\n            return {\n                ...quotation,\n                subtotal: Math.round(subtotal * 100) / 100,\n                total: Math.round(finalTotal * 100) / 100\n            };\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            quotations: quotationsWithTotals,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching quotations:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch quotations\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/quotations - Create new quotation\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = quotationSchema.parse(body);\n        // Generate quotation number\n        const currentYear = new Date().getFullYear();\n        const lastQuotation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.quotation.findFirst({\n            where: {\n                companyId: session.user.companyId || undefined,\n                quotationNumber: {\n                    startsWith: `QUO-${currentYear}-`\n                }\n            },\n            orderBy: {\n                quotationNumber: \"desc\"\n            }\n        });\n        let nextNumber = 1;\n        if (lastQuotation) {\n            const lastNumber = parseInt(lastQuotation.quotationNumber.split(\"-\")[2]);\n            nextNumber = lastNumber + 1;\n        }\n        const quotationNumber = `QUO-${currentYear}-${nextNumber.toString().padStart(4, \"0\")}`;\n        // Prepare quotation data\n        const quotationData = {\n            ...validatedData,\n            quotationNumber,\n            companyId: session.user.companyId,\n            createdById: session.user.id\n        };\n        if (validatedData.validUntil) {\n            quotationData.validUntil = new Date(validatedData.validUntil);\n        }\n        // Create quotation with items in a transaction\n        const quotation = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.$transaction(async (tx)=>{\n            const newQuotation = await tx.quotation.create({\n                data: {\n                    ...quotationData,\n                    items: {\n                        create: validatedData.items.map((item)=>({\n                                ...item,\n                                companyId: session.user.companyId\n                            }))\n                    }\n                },\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            company: true\n                        }\n                    },\n                    lead: {\n                        select: {\n                            id: true,\n                            name: true,\n                            status: true\n                        }\n                    },\n                    createdBy: {\n                        select: {\n                            name: true,\n                            email: true\n                        }\n                    },\n                    items: {\n                        orderBy: {\n                            createdAt: \"asc\"\n                        }\n                    }\n                }\n            });\n            // Log activity\n            await tx.activity.create({\n                data: {\n                    type: \"NOTE\",\n                    title: \"Quotation Created\",\n                    description: `Quotation \"${newQuotation.title}\" (${quotationNumber}) was created`,\n                    quotationId: newQuotation.id,\n                    customerId: newQuotation.customerId,\n                    leadId: newQuotation.leadId,\n                    companyId: session.user.companyId,\n                    createdById: session.user.id\n                }\n            });\n            return newQuotation;\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(quotation, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error creating quotation:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create quotation\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/quotations/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Using JWT strategy instead of database adapter for now\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        console.log(\"Missing credentials\");\n                        return null;\n                    }\n                    console.log(\"Attempting to authenticate user:\", credentials.email);\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            email: credentials.email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            password: true,\n                            role: true,\n                            companyId: true\n                        }\n                    });\n                    // Get the company ID - either as member or owner\n                    let finalCompanyId = user?.companyId;\n                    if (!finalCompanyId && user) {\n                        const ownedCompany = await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.company.findFirst({\n                            where: {\n                                ownerId: user.id\n                            },\n                            select: {\n                                id: true\n                            }\n                        });\n                        finalCompanyId = ownedCompany?.id;\n                        // If user is company owner, update their companyId for future queries\n                        if (finalCompanyId) {\n                            await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                                where: {\n                                    id: user.id\n                                },\n                                data: {\n                                    companyId: finalCompanyId\n                                }\n                            });\n                        }\n                    }\n                    if (!user) {\n                        console.log(\"User not found:\", credentials.email);\n                        return null;\n                    }\n                    if (!user.password) {\n                        console.log(\"User has no password set:\", credentials.email);\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.log(\"Invalid password for user:\", credentials.email);\n                        return null;\n                    }\n                    // Update last login\n                    await _prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            lastLoginAt: new Date(),\n                            loginCount: {\n                                increment: 1\n                            }\n                        }\n                    });\n                    console.log(\"User authenticated successfully:\", user.email);\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role,\n                        companyId: finalCompanyId\n                    };\n                } catch (error) {\n                    console.error(\"Authentication error:\", error);\n                    return null;\n                }\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"JWT callback - user data:\", {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    companyId: user.companyId\n                });\n                token.role = user.role;\n                token.companyId = user.companyId;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                console.log(\"Session callback - final session:\", {\n                    id: session.user.id,\n                    email: session.user.email,\n                    role: session.user.role,\n                    companyId: session.user.companyId\n                });\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquotations%2Froute&page=%2Fapi%2Fquotations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquotations%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for quotation items
const quotationItemSchema = z.object({
  id: z.string().optional(),
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  discount: z.number().min(0).max(100).optional().default(0),
  taxRate: z.number().min(0).max(100).optional().default(0)
})

// Validation schema for quotation update
const quotationUpdateSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().optional().nullable(),
  customerId: z.string().min(1, 'Customer is required').optional(),
  leadId: z.string().optional().nullable(),
  status: z.enum(['DRAFT', 'SENT', 'VIEWED', 'ACCEPTED', 'REJECTED', 'EXPIRED']).optional(),
  validUntil: z.string().optional().nullable(),
  terms: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  items: z.array(quotationItemSchema).min(1, 'At least one item is required').optional(),
  taxRate: z.number().min(0).max(100).optional(),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).optional(),
  discountValue: z.number().min(0).optional()
})

// GET /api/quotations/[id] - Get single quotation
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const quotation = await prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true, phone: true, address: true, city: true, state: true, country: true, postalCode: true }
        },
        lead: {
          select: { id: true, title: true, status: true }
        },
        createdBy: {
          select: { name: true, email: true }
        },
        items: {
          orderBy: { createdAt: 'asc' }
        },
        activities: {
          orderBy: { createdAt: 'desc' },
          take: 10,
          include: {
            createdBy: { select: { name: true } }
          }
        },
        _count: {
          select: {
            activities: true
          }
        }
      }
    })

    if (!quotation) {
      return NextResponse.json({ error: 'Quotation not found' }, { status: 404 })
    }

    // Calculate totals
    const subtotal = quotation.items.reduce((sum, item) => {
      const itemTotal = item.quantity * item.unitPrice
      const discountAmount = (itemTotal * item.discount) / 100
      const afterDiscount = itemTotal - discountAmount
      const taxAmount = (afterDiscount * item.taxRate) / 100
      return sum + afterDiscount + taxAmount
    }, 0)

    let total = subtotal
    if (quotation.discountType === 'PERCENTAGE') {
      total = subtotal - (subtotal * quotation.discountValue) / 100
    } else {
      total = subtotal - quotation.discountValue
    }

    const finalTaxAmount = (total * quotation.taxRate) / 100
    const finalTotal = total + finalTaxAmount

    const quotationWithTotals = {
      ...quotation,
      subtotal: Math.round(subtotal * 100) / 100,
      total: Math.round(finalTotal * 100) / 100,
      taxAmount: Math.round(finalTaxAmount * 100) / 100,
      discountAmount: quotation.discountType === 'PERCENTAGE' 
        ? Math.round((subtotal * quotation.discountValue / 100) * 100) / 100
        : quotation.discountValue
    }

    return NextResponse.json(quotationWithTotals)
  } catch (error) {
    console.error('Error fetching quotation:', error)
    return NextResponse.json(
      { error: 'Failed to fetch quotation' },
      { status: 500 }
    )
  }
}

// PUT /api/quotations/[id] - Update quotation
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = quotationUpdateSchema.parse(body)

    // Check if quotation exists and belongs to user's company
    const existingQuotation = await prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        items: true
      }
    })

    if (!existingQuotation) {
      return NextResponse.json({ error: 'Quotation not found' }, { status: 404 })
    }

    // Prepare update data
    const updateData: any = { ...validatedData }

    if (validatedData.validUntil) {
      updateData.validUntil = new Date(validatedData.validUntil)
    }

    // Update quotation with items in a transaction
    const quotation = await prisma.$transaction(async (tx) => {
      // Update quotation
      const updatedQuotation = await tx.quotation.update({
        where: { id: params.id },
        data: updateData,
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          lead: {
            select: { id: true, title: true, status: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          items: {
            orderBy: { createdAt: 'asc' }
          }
        }
      })

      // Update items if provided
      if (validatedData.items) {
        // Delete existing items
        await tx.quotationItem.deleteMany({
          where: { quotationId: params.id }
        })

        // Create new items
        await tx.quotationItem.createMany({
          data: validatedData.items.map(item => ({
            ...item,
            quotationId: params.id,
            companyId: session.user.companyId!
          }))
        })

        // Fetch updated quotation with new items
        const finalQuotation = await tx.quotation.findUnique({
          where: { id: params.id },
          include: {
            customer: {
              select: { id: true, name: true, email: true, company: true }
            },
            lead: {
              select: { id: true, title: true, status: true }
            },
            createdBy: {
              select: { name: true, email: true }
            },
            items: {
              orderBy: { createdAt: 'asc' }
            }
          }
        })

        // Log activity if status changed
        if (validatedData.status && validatedData.status !== existingQuotation.status) {
          await tx.activity.create({
            data: {
              type: 'STATUS_CHANGE',
              title: 'Quotation Status Updated',
              description: `Quotation status changed from ${existingQuotation.status} to ${validatedData.status}`,
              quotationId: params.id,
              customerId: updatedQuotation.customerId,
              leadId: updatedQuotation.leadId,
              companyId: session.user.companyId!,
              createdById: session.user.id
            }
          })
        }

        return finalQuotation
      }

      return updatedQuotation
    })

    return NextResponse.json(quotation)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating quotation:', error)
    return NextResponse.json(
      { error: 'Failed to update quotation' },
      { status: 500 }
    )
  }
}

// DELETE /api/quotations/[id] - Delete quotation
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if quotation exists and belongs to user's company
    const quotation = await prisma.quotation.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        _count: {
          select: {
            invoices: true
          }
        }
      }
    })

    if (!quotation) {
      return NextResponse.json({ error: 'Quotation not found' }, { status: 404 })
    }

    // Check if quotation has related invoices
    if (quotation._count.invoices > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete quotation with existing invoices',
          details: quotation._count
        },
        { status: 400 }
      )
    }

    await prisma.quotation.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Quotation deleted successfully' })
  } catch (error) {
    console.error('Error deleting quotation:', error)
    return NextResponse.json(
      { error: 'Failed to delete quotation' },
      { status: 500 }
    )
  }
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/customers/[id]/route";
exports.ids = ["app/api/customers/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_proj_nextjs_saas_app_api_customers_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/customers/[id]/route.ts */ \"(rsc)/./app/api/customers/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/customers/[id]/route\",\n        pathname: \"/api/customers/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/customers/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\api\\\\customers\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_proj_nextjs_saas_app_api_customers_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/customers/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/customers/[id]/route.ts":
/*!*****************************************!*\
  !*** ./app/api/customers/[id]/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n// Validation schema for customer update\nconst customerUpdateSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"Name is required\").optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.string().email(\"Invalid email\").optional().nullable(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    company: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    address: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    city: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    state: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    country: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    postalCode: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    industry: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    website: zod__WEBPACK_IMPORTED_MODULE_4__.string().url(\"Invalid website URL\").optional().nullable().or(zod__WEBPACK_IMPORTED_MODULE_4__.literal(\"\")),\n    notes: zod__WEBPACK_IMPORTED_MODULE_4__.string().optional().nullable(),\n    tags: zod__WEBPACK_IMPORTED_MODULE_4__.array(zod__WEBPACK_IMPORTED_MODULE_4__.string()).optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_4__[\"enum\"]([\n        \"ACTIVE\",\n        \"INACTIVE\",\n        \"PROSPECT\"\n    ]).optional()\n});\n// GET /api/customers/[id] - Get single customer\nasync function GET(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const customer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            },\n            include: {\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                leads: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 5,\n                    include: {\n                        createdBy: {\n                            select: {\n                                name: true\n                            }\n                        }\n                    }\n                },\n                quotations: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 5,\n                    include: {\n                        createdBy: {\n                            select: {\n                                name: true\n                            }\n                        }\n                    }\n                },\n                invoices: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 5,\n                    include: {\n                        createdBy: {\n                            select: {\n                                name: true\n                            }\n                        }\n                    }\n                },\n                activities: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 10,\n                    include: {\n                        createdBy: {\n                            select: {\n                                name: true\n                            }\n                        }\n                    }\n                },\n                _count: {\n                    select: {\n                        leads: true,\n                        quotations: true,\n                        invoices: true,\n                        contracts: true,\n                        activities: true\n                    }\n                }\n            }\n        });\n        if (!customer) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Customer not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(customer);\n    } catch (error) {\n        console.error(\"Error fetching customer:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch customer\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/customers/[id] - Update customer\nasync function PUT(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const validatedData = customerUpdateSchema.parse(body);\n        // Check if customer exists and belongs to user's company\n        const existingCustomer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            }\n        });\n        if (!existingCustomer) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Customer not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if email is being changed and if it conflicts with another customer\n        if (validatedData.email && validatedData.email !== existingCustomer.email) {\n            const emailConflict = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.findFirst({\n                where: {\n                    email: validatedData.email,\n                    companyId: session.user.companyId || undefined,\n                    id: {\n                        not: params.id\n                    }\n                }\n            });\n            if (emailConflict) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Customer with this email already exists\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        const customer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.update({\n            where: {\n                id: params.id\n            },\n            data: validatedData,\n            include: {\n                createdBy: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                _count: {\n                    select: {\n                        leads: true,\n                        quotations: true,\n                        invoices: true,\n                        activities: true\n                    }\n                }\n            }\n        });\n        // Log activity\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.activity.create({\n            data: {\n                type: \"NOTE\",\n                title: \"Customer Updated\",\n                description: `Customer \"${customer.name}\" was updated`,\n                customerId: customer.id,\n                companyId: session.user.companyId,\n                createdById: session.user.id\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(customer);\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_5__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Validation failed\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error(\"Error updating customer:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to update customer\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/customers/[id] - Delete customer\nasync function DELETE(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if customer exists and belongs to user's company\n        const customer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.findFirst({\n            where: {\n                id: params.id,\n                companyId: session.user.companyId || undefined\n            },\n            include: {\n                _count: {\n                    select: {\n                        leads: true,\n                        quotations: true,\n                        invoices: true,\n                        contracts: true\n                    }\n                }\n            }\n        });\n        if (!customer) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Customer not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if customer has related records\n        const hasRelatedRecords = customer._count.leads > 0 || customer._count.quotations > 0 || customer._count.invoices > 0 || customer._count.contracts > 0;\n        if (hasRelatedRecords) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Cannot delete customer with existing leads, quotations, invoices, or contracts\",\n                details: customer._count\n            }, {\n                status: 400\n            });\n        }\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.customer.delete({\n            where: {\n                id: params.id\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Customer deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting customer:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to delete customer\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/customers/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_5__.prisma),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    },\n                    include: {\n                        company: true,\n                        ownedCompany: true\n                    }\n                });\n                if (!user || !user.password) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                // Update last login\n                await _prisma__WEBPACK_IMPORTED_MODULE_5__.prisma.user.update({\n                    where: {\n                        id: user.id\n                    },\n                    data: {\n                        lastLoginAt: new Date(),\n                        loginCount: {\n                            increment: 1\n                        }\n                    }\n                });\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    companyId: user.companyId,\n                    company: user.company || user.ownedCompany\n                };\n            }\n        }),\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.companyId = user.companyId;\n                token.company = user.company;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.companyId = token.companyId;\n                session.user.company = token.company;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\",\n        error: \"/auth/error\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUN5RDtBQUNRO0FBQ1Y7QUFDQTtBQUMxQjtBQUNJO0FBRTFCLE1BQU1NLGNBQStCO0lBQzFDQyxTQUFTUCx3RUFBYUEsQ0FBQ0ssMkNBQU1BO0lBQzdCRyxXQUFXO1FBQ1RQLDJFQUFtQkEsQ0FBQztZQUNsQlEsTUFBTTtZQUNOQyxhQUFhO2dCQUNYQyxPQUFPO29CQUFFQyxPQUFPO29CQUFTQyxNQUFNO2dCQUFRO2dCQUN2Q0MsVUFBVTtvQkFBRUYsT0FBTztvQkFBWUMsTUFBTTtnQkFBVztZQUNsRDtZQUNBLE1BQU1FLFdBQVVMLFdBQVc7Z0JBQ3pCLElBQUksQ0FBQ0EsYUFBYUMsU0FBUyxDQUFDRCxhQUFhSSxVQUFVO29CQUNqRCxPQUFPO2dCQUNUO2dCQUVBLE1BQU1FLE9BQU8sTUFBTVgsMkNBQU1BLENBQUNXLElBQUksQ0FBQ0MsVUFBVSxDQUFDO29CQUN4Q0MsT0FBTzt3QkFDTFAsT0FBT0QsWUFBWUMsS0FBSztvQkFDMUI7b0JBQ0FRLFNBQVM7d0JBQ1BDLFNBQVM7d0JBQ1RDLGNBQWM7b0JBQ2hCO2dCQUNGO2dCQUVBLElBQUksQ0FBQ0wsUUFBUSxDQUFDQSxLQUFLRixRQUFRLEVBQUU7b0JBQzNCLE9BQU87Z0JBQ1Q7Z0JBRUEsTUFBTVEsa0JBQWtCLE1BQU1sQix1REFBYyxDQUMxQ00sWUFBWUksUUFBUSxFQUNwQkUsS0FBS0YsUUFBUTtnQkFHZixJQUFJLENBQUNRLGlCQUFpQjtvQkFDcEIsT0FBTztnQkFDVDtnQkFFQSxvQkFBb0I7Z0JBQ3BCLE1BQU1qQiwyQ0FBTUEsQ0FBQ1csSUFBSSxDQUFDUSxNQUFNLENBQUM7b0JBQ3ZCTixPQUFPO3dCQUFFTyxJQUFJVCxLQUFLUyxFQUFFO29CQUFDO29CQUNyQkMsTUFBTTt3QkFDSkMsYUFBYSxJQUFJQzt3QkFDakJDLFlBQVk7NEJBQUVDLFdBQVc7d0JBQUU7b0JBQzdCO2dCQUNGO2dCQUVBLE9BQU87b0JBQ0xMLElBQUlULEtBQUtTLEVBQUU7b0JBQ1hkLE9BQU9LLEtBQUtMLEtBQUs7b0JBQ2pCRixNQUFNTyxLQUFLUCxJQUFJO29CQUNmc0IsTUFBTWYsS0FBS2UsSUFBSTtvQkFDZkMsV0FBV2hCLEtBQUtnQixTQUFTO29CQUN6QlosU0FBU0osS0FBS0ksT0FBTyxJQUFJSixLQUFLSyxZQUFZO2dCQUM1QztZQUNGO1FBQ0Y7UUFDQW5CLHNFQUFjQSxDQUFDO1lBQ2IrQixVQUFVQyxRQUFRQyxHQUFHLENBQUNDLGdCQUFnQjtZQUN0Q0MsY0FBY0gsUUFBUUMsR0FBRyxDQUFDRyxvQkFBb0I7UUFDaEQ7UUFDQW5DLHNFQUFjQSxDQUFDO1lBQ2I4QixVQUFVQyxRQUFRQyxHQUFHLENBQUNJLGdCQUFnQjtZQUN0Q0YsY0FBY0gsUUFBUUMsR0FBRyxDQUFDSyxvQkFBb0I7UUFDaEQ7S0FDRDtJQUNEQyxTQUFTO1FBQ1BDLFVBQVU7SUFDWjtJQUNBQyxXQUFXO1FBQ1QsTUFBTUMsS0FBSSxFQUFFQyxLQUFLLEVBQUU3QixJQUFJLEVBQUU7WUFDdkIsSUFBSUEsTUFBTTtnQkFDUjZCLE1BQU1kLElBQUksR0FBR2YsS0FBS2UsSUFBSTtnQkFDdEJjLE1BQU1iLFNBQVMsR0FBR2hCLEtBQUtnQixTQUFTO2dCQUNoQ2EsTUFBTXpCLE9BQU8sR0FBR0osS0FBS0ksT0FBTztZQUM5QjtZQUNBLE9BQU95QjtRQUNUO1FBQ0EsTUFBTUosU0FBUSxFQUFFQSxPQUFPLEVBQUVJLEtBQUssRUFBRTtZQUM5QixJQUFJQSxPQUFPO2dCQUNUSixRQUFRekIsSUFBSSxDQUFDUyxFQUFFLEdBQUdvQixNQUFNQyxHQUFHO2dCQUMzQkwsUUFBUXpCLElBQUksQ0FBQ2UsSUFBSSxHQUFHYyxNQUFNZCxJQUFJO2dCQUM5QlUsUUFBUXpCLElBQUksQ0FBQ2dCLFNBQVMsR0FBR2EsTUFBTWIsU0FBUztnQkFDeENTLFFBQVF6QixJQUFJLENBQUNJLE9BQU8sR0FBR3lCLE1BQU16QixPQUFPO1lBQ3RDO1lBQ0EsT0FBT3FCO1FBQ1Q7SUFDRjtJQUNBTSxPQUFPO1FBQ0xDLFFBQVE7UUFDUkMsUUFBUTtRQUNSQyxPQUFPO0lBQ1Q7QUFDRixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNhYXMtYXBwLy4vbGliL2F1dGgudHM/YmY3ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0QXV0aE9wdGlvbnMgfSBmcm9tICduZXh0LWF1dGgnXG5pbXBvcnQgeyBQcmlzbWFBZGFwdGVyIH0gZnJvbSAnQG5leHQtYXV0aC9wcmlzbWEtYWRhcHRlcidcbmltcG9ydCBDcmVkZW50aWFsc1Byb3ZpZGVyIGZyb20gJ25leHQtYXV0aC9wcm92aWRlcnMvY3JlZGVudGlhbHMnXG5pbXBvcnQgR29vZ2xlUHJvdmlkZXIgZnJvbSAnbmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGUnXG5pbXBvcnQgR2l0SHViUHJvdmlkZXIgZnJvbSAnbmV4dC1hdXRoL3Byb3ZpZGVycy9naXRodWInXG5pbXBvcnQgYmNyeXB0IGZyb20gJ2JjcnlwdGpzJ1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnLi9wcmlzbWEnXG5cbmV4cG9ydCBjb25zdCBhdXRoT3B0aW9uczogTmV4dEF1dGhPcHRpb25zID0ge1xuICBhZGFwdGVyOiBQcmlzbWFBZGFwdGVyKHByaXNtYSksXG4gIHByb3ZpZGVyczogW1xuICAgIENyZWRlbnRpYWxzUHJvdmlkZXIoe1xuICAgICAgbmFtZTogJ2NyZWRlbnRpYWxzJyxcbiAgICAgIGNyZWRlbnRpYWxzOiB7XG4gICAgICAgIGVtYWlsOiB7IGxhYmVsOiAnRW1haWwnLCB0eXBlOiAnZW1haWwnIH0sXG4gICAgICAgIHBhc3N3b3JkOiB7IGxhYmVsOiAnUGFzc3dvcmQnLCB0eXBlOiAncGFzc3dvcmQnIH1cbiAgICAgIH0sXG4gICAgICBhc3luYyBhdXRob3JpemUoY3JlZGVudGlhbHMpIHtcbiAgICAgICAgaWYgKCFjcmVkZW50aWFscz8uZW1haWwgfHwgIWNyZWRlbnRpYWxzPy5wYXNzd29yZCkge1xuICAgICAgICAgIHJldHVybiBudWxsXG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCB1c2VyID0gYXdhaXQgcHJpc21hLnVzZXIuZmluZFVuaXF1ZSh7XG4gICAgICAgICAgd2hlcmU6IHtcbiAgICAgICAgICAgIGVtYWlsOiBjcmVkZW50aWFscy5lbWFpbFxuICAgICAgICAgIH0sXG4gICAgICAgICAgaW5jbHVkZToge1xuICAgICAgICAgICAgY29tcGFueTogdHJ1ZSxcbiAgICAgICAgICAgIG93bmVkQ29tcGFueTogdHJ1ZVxuICAgICAgICAgIH1cbiAgICAgICAgfSlcblxuICAgICAgICBpZiAoIXVzZXIgfHwgIXVzZXIucGFzc3dvcmQpIHtcbiAgICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgaXNQYXNzd29yZFZhbGlkID0gYXdhaXQgYmNyeXB0LmNvbXBhcmUoXG4gICAgICAgICAgY3JlZGVudGlhbHMucGFzc3dvcmQsXG4gICAgICAgICAgdXNlci5wYXNzd29yZFxuICAgICAgICApXG5cbiAgICAgICAgaWYgKCFpc1Bhc3N3b3JkVmFsaWQpIHtcbiAgICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgICB9XG5cbiAgICAgICAgLy8gVXBkYXRlIGxhc3QgbG9naW5cbiAgICAgICAgYXdhaXQgcHJpc21hLnVzZXIudXBkYXRlKHtcbiAgICAgICAgICB3aGVyZTogeyBpZDogdXNlci5pZCB9LFxuICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgIGxhc3RMb2dpbkF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgbG9naW5Db3VudDogeyBpbmNyZW1lbnQ6IDEgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSlcblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlkOiB1c2VyLmlkLFxuICAgICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxuICAgICAgICAgIG5hbWU6IHVzZXIubmFtZSxcbiAgICAgICAgICByb2xlOiB1c2VyLnJvbGUsXG4gICAgICAgICAgY29tcGFueUlkOiB1c2VyLmNvbXBhbnlJZCxcbiAgICAgICAgICBjb21wYW55OiB1c2VyLmNvbXBhbnkgfHwgdXNlci5vd25lZENvbXBhbnlcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pLFxuICAgIEdvb2dsZVByb3ZpZGVyKHtcbiAgICAgIGNsaWVudElkOiBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX0lEISxcbiAgICAgIGNsaWVudFNlY3JldDogcHJvY2Vzcy5lbnYuR09PR0xFX0NMSUVOVF9TRUNSRVQhLFxuICAgIH0pLFxuICAgIEdpdEh1YlByb3ZpZGVyKHtcbiAgICAgIGNsaWVudElkOiBwcm9jZXNzLmVudi5HSVRIVUJfQ0xJRU5UX0lEISxcbiAgICAgIGNsaWVudFNlY3JldDogcHJvY2Vzcy5lbnYuR0lUSFVCX0NMSUVOVF9TRUNSRVQhLFxuICAgIH0pXG4gIF0sXG4gIHNlc3Npb246IHtcbiAgICBzdHJhdGVneTogJ2p3dCdcbiAgfSxcbiAgY2FsbGJhY2tzOiB7XG4gICAgYXN5bmMgand0KHsgdG9rZW4sIHVzZXIgfSkge1xuICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgdG9rZW4ucm9sZSA9IHVzZXIucm9sZVxuICAgICAgICB0b2tlbi5jb21wYW55SWQgPSB1c2VyLmNvbXBhbnlJZFxuICAgICAgICB0b2tlbi5jb21wYW55ID0gdXNlci5jb21wYW55XG4gICAgICB9XG4gICAgICByZXR1cm4gdG9rZW5cbiAgICB9LFxuICAgIGFzeW5jIHNlc3Npb24oeyBzZXNzaW9uLCB0b2tlbiB9KSB7XG4gICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgc2Vzc2lvbi51c2VyLmlkID0gdG9rZW4uc3ViIVxuICAgICAgICBzZXNzaW9uLnVzZXIucm9sZSA9IHRva2VuLnJvbGUgYXMgc3RyaW5nXG4gICAgICAgIHNlc3Npb24udXNlci5jb21wYW55SWQgPSB0b2tlbi5jb21wYW55SWQgYXMgc3RyaW5nXG4gICAgICAgIHNlc3Npb24udXNlci5jb21wYW55ID0gdG9rZW4uY29tcGFueSBhcyBhbnlcbiAgICAgIH1cbiAgICAgIHJldHVybiBzZXNzaW9uXG4gICAgfVxuICB9LFxuICBwYWdlczoge1xuICAgIHNpZ25JbjogJy9hdXRoL3NpZ25pbicsXG4gICAgc2lnblVwOiAnL2F1dGgvc2lnbnVwJyxcbiAgICBlcnJvcjogJy9hdXRoL2Vycm9yJ1xuICB9XG59XG4iXSwibmFtZXMiOlsiUHJpc21hQWRhcHRlciIsIkNyZWRlbnRpYWxzUHJvdmlkZXIiLCJHb29nbGVQcm92aWRlciIsIkdpdEh1YlByb3ZpZGVyIiwiYmNyeXB0IiwicHJpc21hIiwiYXV0aE9wdGlvbnMiLCJhZGFwdGVyIiwicHJvdmlkZXJzIiwibmFtZSIsImNyZWRlbnRpYWxzIiwiZW1haWwiLCJsYWJlbCIsInR5cGUiLCJwYXNzd29yZCIsImF1dGhvcml6ZSIsInVzZXIiLCJmaW5kVW5pcXVlIiwid2hlcmUiLCJpbmNsdWRlIiwiY29tcGFueSIsIm93bmVkQ29tcGFueSIsImlzUGFzc3dvcmRWYWxpZCIsImNvbXBhcmUiLCJ1cGRhdGUiLCJpZCIsImRhdGEiLCJsYXN0TG9naW5BdCIsIkRhdGUiLCJsb2dpbkNvdW50IiwiaW5jcmVtZW50Iiwicm9sZSIsImNvbXBhbnlJZCIsImNsaWVudElkIiwicHJvY2VzcyIsImVudiIsIkdPT0dMRV9DTElFTlRfSUQiLCJjbGllbnRTZWNyZXQiLCJHT09HTEVfQ0xJRU5UX1NFQ1JFVCIsIkdJVEhVQl9DTElFTlRfSUQiLCJHSVRIVUJfQ0xJRU5UX1NFQ1JFVCIsInNlc3Npb24iLCJzdHJhdGVneSIsImNhbGxiYWNrcyIsImp3dCIsInRva2VuIiwic3ViIiwicGFnZXMiLCJzaWduSW4iLCJzaWduVXAiLCJlcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL2xpYi9wcmlzbWEudHM/OTgyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5Cproj%5Cnextjs-saas%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cproj%5Cnextjs-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for invoice items
const invoiceItemSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  itemId: z.string().optional()
})

// Validation schema for invoice creation/update
const invoiceSchema = z.object({
  invoiceNumber: z.string().optional(), // Auto-generated if not provided
  title: z.string().optional(),
  customerId: z.string().min(1, 'Customer is required'),
  quotationId: z.string().optional().nullable(),
  status: z.enum(['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED']).default('DRAFT'),
  dueDate: z.string().optional(),
  terms: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  items: z.array(invoiceItemSchema).min(1, 'At least one item is required'),
  taxRate: z.number().min(0).max(100).optional().default(0)
})

// GET /api/invoices - List invoices with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const customerId = searchParams.get('customerId') || ''
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId || undefined
    }

    if (search) {
      where.OR = [
        { invoiceNumber: { contains: search, mode: 'insensitive' } },
        { customer: { name: { contains: search, mode: 'insensitive' } } },
        { customer: { companyName: { contains: search, mode: 'insensitive' } } }
      ]
    }

    if (status) {
      where.status = status
    }

    if (customerId) {
      where.customerId = customerId
    }

    // Get invoices with pagination
    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          customer: {
            select: { id: true, name: true, email: true, companyName: true }
          },
          quotation: {
            select: { id: true, quotationNumber: true, title: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          items: true,
          transactions: true
        }
      }),
      prisma.invoice.count({ where })
    ])

    // Return invoices with existing totals from database
    const invoicesWithTotals = invoices.map(invoice => {
      // Convert all potential BigInt fields to strings/numbers
      const cleanInvoice = JSON.parse(JSON.stringify(invoice, (key, value) =>
        typeof value === 'bigint' ? value.toString() : value
      ))

      return {
        ...cleanInvoice,
        subtotal: Number(cleanInvoice.subtotal),
        total: Number(cleanInvoice.total),
        taxAmount: Number(cleanInvoice.taxAmount),
        paidAmount: Number(cleanInvoice.paidAmount),
        balanceAmount: Number(cleanInvoice.balanceAmount)
      }
    })

    return NextResponse.json({
      invoices: invoicesWithTotals,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching invoices:', error)
    return NextResponse.json(
      { error: 'Failed to fetch invoices' },
      { status: 500 }
    )
  }
}

// POST /api/invoices - Create new invoice
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = invoiceSchema.parse(body)

    // Generate invoice number if not provided
    let invoiceNumber = validatedData.invoiceNumber
    if (!invoiceNumber) {
      const currentYear = new Date().getFullYear()
      const lastInvoice = await prisma.invoice.findFirst({
        where: {
          companyId: session.user.companyId || undefined,
          invoiceNumber: {
            startsWith: `INV-${currentYear}-`
          }
        },
        orderBy: { invoiceNumber: 'desc' }
      })

      let nextNumber = 1
      if (lastInvoice) {
        const lastNumber = parseInt(lastInvoice.invoiceNumber.split('-')[2])
        nextNumber = lastNumber + 1
      }

      invoiceNumber = `INV-${currentYear}-${nextNumber.toString().padStart(4, '0')}`
    }

    // Calculate totals
    const subtotal = validatedData.items.reduce((sum, item) => {
      return sum + (item.quantity * item.unitPrice)
    }, 0)

    const taxAmount = (subtotal * (validatedData.taxRate || 0)) / 100
    const total = subtotal + taxAmount

    // Prepare invoice data
    const invoiceData: any = {
      invoiceNumber,
      title: validatedData.title || `Invoice ${invoiceNumber}`,
      customerId: validatedData.customerId,
      quotationId: validatedData.quotationId,
      status: validatedData.status,
      subtotal,
      taxRate: validatedData.taxRate || 0,
      taxAmount,
      total,
      terms: validatedData.terms,
      notes: validatedData.notes,
      companyId: session.user.companyId!,
      createdById: session.user.id
    }

    if (validatedData.dueDate) {
      invoiceData.dueDate = new Date(validatedData.dueDate)
    } else {
      // Default to 30 days from now
      const dueDate = new Date()
      dueDate.setDate(dueDate.getDate() + 30)
      invoiceData.dueDate = dueDate
    }

    // Create invoice with items in a transaction
    const invoice = await prisma.$transaction(async (tx) => {
      const newInvoice = await tx.invoice.create({
        data: {
          ...invoiceData,
          items: {
            create: validatedData.items.map(item => ({
              name: item.name,
              description: item.description,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              total: item.quantity * item.unitPrice,
              itemId: item.itemId
            }))
          }
        },
        include: {
          customer: {
            select: { id: true, name: true, email: true, companyName: true }
          },
          quotation: {
            select: { id: true, quotationNumber: true, title: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          items: true
        }
      })

      // If created from quotation, update quotation status
      if (newInvoice.quotationId) {
        await tx.quotation.update({
          where: { id: newInvoice.quotationId },
          data: { status: 'ACCEPTED' }
        })
      }

      return newInvoice
    })

    return NextResponse.json(invoice, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating invoice:', error)
    return NextResponse.json(
      { error: 'Failed to create invoice' },
      { status: 500 }
    )
  }
}

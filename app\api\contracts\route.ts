import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for contract creation/update
const contractSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional().nullable(),
  customerId: z.string().min(1, 'Customer is required'),
  quotationId: z.string().optional().nullable(),
  invoiceId: z.string().optional().nullable(),
  type: z.enum(['SERVICE', 'PRODUCT', 'SUBSCRIPTION', 'MAINTENANCE', 'CONSULTING', 'OTHER']).default('SERVICE'),
  status: z.enum(['DRAFT', 'REVIEW', 'SENT', 'SIGNED', 'ACTIVE', 'COMPLETED', 'CANCELLED', 'EXPIRED']).default('DRAFT'),
  value: z.number().min(0, 'Contract value must be positive').optional().nullable(),
  currency: z.string().default('USD'),
  startDate: z.string().optional().nullable(),
  endDate: z.string().optional().nullable(),
  renewalDate: z.string().optional().nullable(),
  autoRenewal: z.boolean().default(false),
  renewalPeriod: z.number().optional().nullable(), // in months
  terms: z.string().optional().nullable(),
  conditions: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  templateId: z.string().optional().nullable(),
  signatureRequired: z.boolean().default(true),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  tags: z.array(z.string()).optional().default([])
})

// GET /api/contracts - List contracts with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const type = searchParams.get('type') || ''
    const customerId = searchParams.get('customerId') || ''
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      companyId: session.user.companyId || undefined
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { contractNumber: { contains: search, mode: 'insensitive' } },
        { customer: { name: { contains: search, mode: 'insensitive' } } },
        { customer: { company: { contains: search, mode: 'insensitive' } } }
      ]
    }

    if (status) {
      where.status = status
    }

    if (type) {
      where.type = type
    }

    if (customerId) {
      where.customerId = customerId
    }

    // Get contracts with pagination
    const [contracts, total] = await Promise.all([
      prisma.contract.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          quotation: {
            select: { id: true, quotationNumber: true, title: true }
          },
          invoice: {
            select: { id: true, invoiceNumber: true }
          },
          template: {
            select: { id: true, name: true, type: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          assignedTo: {
            select: { name: true, email: true }
          },
          _count: {
            select: {
              activities: true,
              signatures: true,
              documents: true
            }
          }
        }
      }),
      prisma.contract.count({ where })
    ])

    return NextResponse.json({
      contracts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching contracts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch contracts' },
      { status: 500 }
    )
  }
}

// POST /api/contracts - Create new contract
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = contractSchema.parse(body)

    // Generate contract number
    const currentYear = new Date().getFullYear()
    const lastContract = await prisma.contract.findFirst({
      where: {
        companyId: session.user.companyId || undefined,
        contractNumber: {
          startsWith: `CON-${currentYear}-`
        }
      },
      orderBy: { contractNumber: 'desc' }
    })

    let nextNumber = 1
    if (lastContract) {
      const lastNumber = parseInt(lastContract.contractNumber.split('-')[2])
      nextNumber = lastNumber + 1
    }

    const contractNumber = `CON-${currentYear}-${nextNumber.toString().padStart(4, '0')}`

    // Prepare contract data
    const contractData: any = {
      ...validatedData,
      contractNumber,
      companyId: session.user.companyId!,
      createdById: session.user.id,
      assignedToId: session.user.id // Default to creator
    }

    if (validatedData.startDate) {
      contractData.startDate = new Date(validatedData.startDate)
    }

    if (validatedData.endDate) {
      contractData.endDate = new Date(validatedData.endDate)
    }

    if (validatedData.renewalDate) {
      contractData.renewalDate = new Date(validatedData.renewalDate)
    }

    // Create contract in a transaction
    const contract = await prisma.$transaction(async (tx) => {
      const newContract = await tx.contract.create({
        data: contractData,
        include: {
          customer: {
            select: { id: true, name: true, email: true, company: true }
          },
          quotation: {
            select: { id: true, quotationNumber: true, title: true }
          },
          invoice: {
            select: { id: true, invoiceNumber: true }
          },
          template: {
            select: { id: true, name: true, type: true }
          },
          createdBy: {
            select: { name: true, email: true }
          },
          assignedTo: {
            select: { name: true, email: true }
          }
        }
      })

      // Log activity
      await tx.activity.create({
        data: {
          type: 'NOTE',
          title: 'Contract Created',
          description: `Contract "${newContract.title}" (${contractNumber}) was created`,
          contractId: newContract.id,
          customerId: newContract.customerId,
          quotationId: newContract.quotationId,
          invoiceId: newContract.invoiceId,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })

      return newContract
    })

    return NextResponse.json(contract, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating contract:', error)
    return NextResponse.json(
      { error: 'Failed to create contract' },
      { status: 500 }
    )
  }
}

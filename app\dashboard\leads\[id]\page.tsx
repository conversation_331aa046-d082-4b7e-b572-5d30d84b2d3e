'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { LeadForm } from '@/components/leads/lead-form'
import { ActivityTimeline } from '@/components/leads/activity-timeline'
import { LeadNotes } from '@/components/leads/lead-notes'
import { LeadScoring } from '@/components/leads/lead-scoring'
import { LeadTemperatureDetailed } from '@/components/leads/lead-temperature'
import {
  ArrowLeft,
  Edit,
  Trash2,
  Mail,
  Phone,
  Building2,
  Globe,
  MapPin,
  DollarSign,
  Calendar,
  Target,
  User,
  Activity,
  FileText,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Users
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import Link from 'next/link'

interface Lead {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string | null
  companyName: string | null
  title: string | null
  website: string | null
  source: string
  status: string
  priority: string
  address: string | null
  city: string | null
  state: string | null
  country: string | null
  postalCode: string | null
  industry: string | null
  companySize: string | null
  budget: number | null
  timeline: string | null
  score: number
  qualified: boolean
  qualifiedAt: string | null
  convertedAt: string | null
  customerId: string | null
  customer: {
    id: string
    name: string
    companyName: string | null
  } | null
  assignedToId: string | null
  assignedTo: {
    id: string
    name: string | null
    email: string
  } | null
  description: string | null
  createdAt: string
  updatedAt: string
  _count: {
    activities: number
    leadNotes: number
    tasks: number
    documents: number
  }
}

export default function LeadDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const [lead, setLead] = useState<Lead | null>(null)
  const [loading, setLoading] = useState(true)
  const [showEditForm, setShowEditForm] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const fetchLead = async () => {
    try {
      const response = await fetch(`/api/leads/${params.id}`)
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Lead not found')
          router.push('/dashboard/leads')
          return
        }
        throw new Error('Failed to fetch lead')
      }

      const data = await response.json()
      setLead(data.lead)
    } catch (error) {
      toast.error('Failed to load lead details')
      console.error('Error fetching lead:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (params.id) {
      fetchLead()
    }
  }, [params.id])

  const handleDelete = async () => {
    if (!lead || !confirm(`Are you sure you want to delete "${lead.firstName} ${lead.lastName}"?`)) return

    try {
      const response = await fetch(`/api/leads/${lead.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete lead')
      }

      toast.success('Lead deleted successfully')
      router.push('/dashboard/leads')
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete lead')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'NEW':
        return <Badge variant="secondary">New</Badge>
      case 'CONTACTED':
        return <Badge className="bg-blue-100 text-blue-800">Contacted</Badge>
      case 'QUALIFIED':
        return <Badge className="bg-green-100 text-green-800">Qualified</Badge>
      case 'PROPOSAL':
        return <Badge className="bg-yellow-100 text-yellow-800">Proposal</Badge>
      case 'NEGOTIATION':
        return <Badge className="bg-orange-100 text-orange-800">Negotiation</Badge>
      case 'CLOSED_WON':
        return <Badge className="bg-green-100 text-green-800">Closed Won</Badge>
      case 'CLOSED_LOST':
        return <Badge variant="destructive">Closed Lost</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return <Badge variant="secondary" className="text-xs">Low</Badge>
      case 'MEDIUM':
        return <Badge className="bg-blue-100 text-blue-800 text-xs">Medium</Badge>
      case 'HIGH':
        return <Badge className="bg-orange-100 text-orange-800 text-xs">High</Badge>
      case 'URGENT':
        return <Badge variant="destructive" className="text-xs">Urgent</Badge>
      default:
        return <Badge variant="secondary" className="text-xs">{priority}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!lead) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Lead Not Found</h3>
          <p className="text-gray-500 mb-4">The lead you're looking for doesn't exist.</p>
          <Button asChild>
            <Link href="/dashboard/leads">Back to Leads</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/leads">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Leads
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {lead.firstName} {lead.lastName}
            </h1>
            <p className="text-gray-500">
              {lead.title && lead.companyName ? `${lead.title} at ${lead.companyName}` : 
               lead.title || lead.companyName || 'Lead Details'}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <LeadTemperatureDetailed score={lead.score} />
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => setShowEditForm(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-500">Status</p>
                {getStatusBadge(lead.status)}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-500">Priority</p>
                {getPriorityBadge(lead.priority)}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-500">Score</p>
                <p className="font-semibold">{lead.score}/100</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-500">Budget</p>
                <p className="font-semibold">
                  {lead.budget ? `$${lead.budget.toLocaleString()}` : 'Not specified'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="activities">Activities ({lead._count.activities})</TabsTrigger>
          <TabsTrigger value="notes">Notes ({lead._count.leadNotes})</TabsTrigger>
          <TabsTrigger value="scoring">Scoring & Qualification</TabsTrigger>
          <TabsTrigger value="tasks">Tasks ({lead._count.tasks})</TabsTrigger>
          <TabsTrigger value="documents">Documents ({lead._count.documents})</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{lead.email}</p>
                  </div>
                </div>
                {lead.phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Phone</p>
                      <p className="font-medium">{lead.phone}</p>
                    </div>
                  </div>
                )}
                {lead.website && (
                  <div className="flex items-center space-x-3">
                    <Globe className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Website</p>
                      <a href={lead.website} target="_blank" rel="noopener noreferrer" 
                         className="font-medium text-blue-600 hover:underline">
                        {lead.website}
                      </a>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building2 className="h-5 w-5 mr-2" />
                  Company Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {lead.companyName && (
                  <div>
                    <p className="text-sm text-gray-500">Company</p>
                    <p className="font-medium">{lead.companyName}</p>
                  </div>
                )}
                {lead.title && (
                  <div>
                    <p className="text-sm text-gray-500">Job Title</p>
                    <p className="font-medium">{lead.title}</p>
                  </div>
                )}
                {lead.industry && (
                  <div>
                    <p className="text-sm text-gray-500">Industry</p>
                    <p className="font-medium">{lead.industry}</p>
                  </div>
                )}
                {lead.companySize && (
                  <div>
                    <p className="text-sm text-gray-500">Company Size</p>
                    <p className="font-medium">{lead.companySize}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="activities">
          <ActivityTimeline leadId={lead.id} />
        </TabsContent>

        <TabsContent value="notes">
          <LeadNotes leadId={lead.id} />
        </TabsContent>

        <TabsContent value="scoring">
          <LeadScoring leadId={lead.id} />
        </TabsContent>

        <TabsContent value="tasks">
          <Card>
            <CardHeader>
              <CardTitle>Related Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Task management will be implemented next</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <CardTitle>Documents</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Document management will be implemented next</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Form Modal */}
      {showEditForm && (
        <LeadForm
          isOpen={showEditForm}
          onClose={() => setShowEditForm(false)}
          onSuccess={() => {
            setShowEditForm(false)
            fetchLead()
          }}
          lead={lead}
          mode="edit"
        />
      )}
    </div>
  )
}

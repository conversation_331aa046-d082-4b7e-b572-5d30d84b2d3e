"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowDownRight,ArrowUpRight,BarChart3,Calendar,CheckCircle,Clock,DollarSign,FileCheck,FileText,Plus,Receipt,Target,TrendingUp,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// import { Skeleton } from '@/components/ui/skeleton'\n\nfunction DashboardPage() {\n    var _session_user, _session_user1, _session_user2, _session_user3;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        var _session_user;\n        const fetchDashboardData = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/dashboard\");\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch dashboard data\");\n                }\n                const data = await response.json();\n                setDashboardData(data);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"An error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.companyId) {\n            fetchDashboardData();\n        }\n    }, [\n        session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.companyId\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 77,\n            columnNumber: 12\n        }, this);\n    }\n    if (error || !dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Error Loading Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: error || \"Failed to load dashboard data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        className: \"mt-4\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    const stats = [\n        {\n            title: \"Total Customers\",\n            value: dashboardData.stats.customers.total.toLocaleString(),\n            change: \"\".concat(dashboardData.stats.customers.change >= 0 ? \"+\" : \"\").concat(dashboardData.stats.customers.change.toFixed(1), \"%\"),\n            changeType: dashboardData.stats.customers.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"text-blue-600\",\n            subtitle: \"\".concat(dashboardData.stats.customers.current, \" this month\")\n        },\n        {\n            title: \"Active Leads\",\n            value: dashboardData.stats.leads.current.toLocaleString(),\n            change: \"\".concat(dashboardData.stats.leads.change >= 0 ? \"+\" : \"\").concat(dashboardData.stats.leads.change.toFixed(1), \"%\"),\n            changeType: dashboardData.stats.leads.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"text-green-600\",\n            subtitle: \"\".concat(dashboardData.stats.leads.total, \" total leads\")\n        },\n        {\n            title: \"Quotations\",\n            value: dashboardData.stats.quotations.current.toLocaleString(),\n            change: \"\".concat(dashboardData.stats.quotations.change >= 0 ? \"+\" : \"\").concat(dashboardData.stats.quotations.change.toFixed(1), \"%\"),\n            changeType: dashboardData.stats.quotations.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"text-yellow-600\",\n            subtitle: \"\".concat(dashboardData.stats.quotations.total, \" total\")\n        },\n        {\n            title: \"Revenue\",\n            value: \"$\".concat(dashboardData.stats.revenue.total.toLocaleString()),\n            change: \"\".concat(dashboardData.stats.revenue.change >= 0 ? \"+\" : \"\").concat(dashboardData.stats.revenue.change.toFixed(1), \"%\"),\n            changeType: dashboardData.stats.revenue.change >= 0 ? \"increase\" : \"decrease\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"text-purple-600\",\n            subtitle: \"$\".concat(dashboardData.stats.revenue.pending.toLocaleString(), \" pending\")\n        },\n        {\n            title: \"Active Contracts\",\n            value: dashboardData.stats.contracts.current.toLocaleString(),\n            change: \"\",\n            changeType: \"neutral\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"text-indigo-600\",\n            subtitle: \"\".concat(dashboardData.stats.contracts.total, \" total\")\n        },\n        {\n            title: \"Open Tasks\",\n            value: dashboardData.stats.tasks.current.toLocaleString(),\n            change: \"\",\n            changeType: \"neutral\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            color: \"text-orange-600\",\n            subtitle: \"\".concat(dashboardData.stats.tasks.total, \" total tasks\")\n        }\n    ];\n    // Process recent activities\n    const processedActivities = dashboardData.recentActivities.map((activity)=>{\n        var _activity_createdBy;\n        let message = activity.description;\n        let icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n        let status = \"info\";\n        // Customize based on activity type\n        switch(activity.type){\n            case \"NOTE\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                status = \"info\";\n                break;\n            case \"CALL\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                status = \"success\";\n                break;\n            case \"EMAIL\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n                status = \"info\";\n                break;\n            case \"MEETING\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"];\n                status = \"warning\";\n                break;\n            case \"STATUS_CHANGE\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"];\n                status = \"success\";\n                break;\n            case \"PAYMENT_RECEIVED\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n                status = \"success\";\n                break;\n            case \"CONTRACT_SIGNED\":\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n                status = \"success\";\n                break;\n            default:\n                icon = _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n                status = \"info\";\n        }\n        return {\n            id: activity.id,\n            type: activity.type,\n            message: activity.title,\n            description: activity.description,\n            time: new Date(activity.createdAt).toLocaleString(),\n            icon,\n            status,\n            user: ((_activity_createdBy = activity.createdBy) === null || _activity_createdBy === void 0 ? void 0 : _activity_createdBy.name) || \"System\"\n        };\n    });\n    const quickActions = [\n        {\n            title: \"Add Lead\",\n            description: \"Track a new business lead\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/dashboard/leads/new\",\n            color: \"text-green-600\"\n        },\n        {\n            title: \"Add Customer\",\n            description: \"Create a new customer profile\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/dashboard/customers/new\",\n            color: \"text-blue-600\"\n        },\n        {\n            title: \"Create Quotation\",\n            description: \"Generate a new quotation\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            href: \"/dashboard/quotations/new\",\n            color: \"text-yellow-600\"\n        },\n        {\n            title: \"Create Invoice\",\n            description: \"Generate a new invoice\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            href: \"/dashboard/invoices/new\",\n            color: \"text-purple-600\"\n        },\n        {\n            title: \"New Contract\",\n            description: \"Create a new contract\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            href: \"/dashboard/contracts/new\",\n            color: \"text-indigo-600\"\n        },\n        {\n            title: \"View Reports\",\n            description: \"Analyze your business data\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            href: \"/dashboard/reports\",\n            color: \"text-orange-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: [\n                            \"Welcome back, \",\n                            (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.name) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.email) || \"User\",\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-100\",\n                        children: \"Here's what's happening with your business today.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center space-x-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"mr-2\",\n                                        children: (session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.role) || \"USER\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Role\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"mr-2 text-white border-white\",\n                                        children: [\n                                            dashboardData.stats.customers.total,\n                                            \" Customers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"mr-2 text-white border-white\",\n                                        children: [\n                                            dashboardData.stats.leads.current,\n                                            \" Active Leads\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\",\n                children: stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"hover:shadow-md transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: stat.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-5 w-5 \".concat(stat.color)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this),\n                                    stat.change && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-1\",\n                                        children: [\n                                            stat.changeType === \"increase\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-600 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 21\n                                            }, this) : stat.changeType === \"decrease\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-600 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium \".concat(stat.changeType === \"increase\" ? \"text-green-600\" : stat.changeType === \"decrease\" ? \"text-red-600\" : \"text-gray-500\"),\n                                                children: stat.change\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this),\n                                            stat.changeType !== \"neutral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 ml-1\",\n                                                children: \"from last month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this),\n                                    stat.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: stat.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, stat.title, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Quick Actions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3\",\n                                    children: quickActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            className: \"h-auto p-3 flex flex-col items-center space-y-2 hover:bg-gray-50\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: action.href,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                        className: \"h-5 w-5 \".concat(action.color)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-xs\",\n                                                                children: action.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: action.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, action.title, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Recent Activity\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 max-h-80 overflow-y-auto\",\n                                    children: processedActivities.length > 0 ? processedActivities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(activity.status === \"success\" ? \"bg-green-100\" : activity.status === \"warning\" ? \"bg-yellow-100\" : \"bg-blue-100\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(activity.icon, {\n                                                        className: \"h-4 w-4 \".concat(activity.status === \"success\" ? \"text-green-600\" : activity.status === \"warning\" ? \"text-yellow-600\" : \"text-blue-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: activity.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: activity.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-1 text-xs text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: activity.user\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mx-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: activity.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No recent activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upcoming Tasks\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: dashboardData.upcomingTasks.length > 0 ? dashboardData.upcomingTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(task.priority === \"HIGH\" ? \"bg-red-100\" : task.priority === \"MEDIUM\" ? \"bg-yellow-100\" : \"bg-green-100\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(task.priority === \"HIGH\" ? \"text-red-600\" : task.priority === \"MEDIUM\" ? \"text-yellow-600\" : \"text-green-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Due: \",\n                                                                new Date(task.dueDate).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        task.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                \"Assigned to: \",\n                                                                task.assignedTo.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No upcoming tasks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileContract, {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upcoming Renewals\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: dashboardData.upcomingRenewals.length > 0 ? dashboardData.upcomingRenewals.map((contract)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full bg-orange-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: contract.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Customer: \",\n                                                                contract.customer.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Renewal: \",\n                                                                new Date(contract.renewalDate).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, contract.id, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowDownRight_ArrowUpRight_BarChart3_Calendar_CheckCircle_Clock_DollarSign_FileCheck_FileText_Plus_Receipt_Target_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No upcoming renewals\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"Je5vi3T2RG38wD3MkwriTKJHO44=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession\n    ];\n});\n_c = DashboardPage;\n// Loading skeleton component\nfunction DashboardSkeleton() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-gray-300 to-gray-400 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                        className: \"h-8 w-64 mb-2 bg-gray-200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                        className: \"h-4 w-96 bg-gray-200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                className: \"h-6 w-20 bg-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                className: \"h-6 w-32 bg-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 492,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\",\n                children: Array.from({\n                    length: 6\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                        className: \"h-4 w-24\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                        className: \"h-8 w-16 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                        className: \"h-4 w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                    className: \"h-6 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3\",\n                                    children: Array.from({\n                                        length: 6\n                                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                            className: \"h-20\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                    className: \"h-6 w-40\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: Array.from({\n                                        length: 5\n                                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                                    className: \"h-10 w-10 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                                            className: \"h-4 w-full mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Skeleton, {\n                                                            className: \"h-3 w-24\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, i, true, {\n                                            fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\proj\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 490,\n        columnNumber: 5\n    }, this);\n}\n_c1 = DashboardSkeleton;\nvar _c, _c1;\n$RefreshReg$(_c, \"DashboardPage\");\n$RefreshReg$(_c1, \"DashboardSkeleton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});
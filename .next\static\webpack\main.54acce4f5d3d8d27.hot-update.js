"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "./node_modules/next/dist/shared/lib/router/router.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/router.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// tslint:disable:no-console\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return Router;\n    },\n    matchesMiddleware: function() {\n        return matchesMiddleware;\n    },\n    createKey: function() {\n        return createKey;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _removetrailingslash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"./node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nconst _routeloader = __webpack_require__(/*! ../../../client/route-loader */ \"./node_modules/next/dist/client/route-loader.js\");\nconst _script = __webpack_require__(/*! ../../../client/script */ \"./node_modules/next/dist/client/script.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ../../../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nconst _denormalizepagepath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nconst _normalizelocalepath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"./node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../mitt */ \"./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _utils = __webpack_require__(/*! ../utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _isdynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _parserelativeurl = __webpack_require__(/*! ./utils/parse-relative-url */ \"./node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js\");\nconst _resolverewrites = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./utils/resolve-rewrites */ \"?506d\"));\nconst _routematcher = __webpack_require__(/*! ./utils/route-matcher */ \"./node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nconst _routeregex = __webpack_require__(/*! ./utils/route-regex */ \"./node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nconst _formaturl = __webpack_require__(/*! ./utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _detectdomainlocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"./node_modules/next/dist/client/detect-domain-locale.js\");\nconst _parsepath = __webpack_require__(/*! ./utils/parse-path */ \"./node_modules/next/dist/shared/lib/router/utils/parse-path.js\");\nconst _addlocale = __webpack_require__(/*! ../../../client/add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _removelocale = __webpack_require__(/*! ../../../client/remove-locale */ \"./node_modules/next/dist/client/remove-locale.js\");\nconst _removebasepath = __webpack_require__(/*! ../../../client/remove-base-path */ \"./node_modules/next/dist/client/remove-base-path.js\");\nconst _addbasepath = __webpack_require__(/*! ../../../client/add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../client/has-base-path */ \"./node_modules/next/dist/client/has-base-path.js\");\nconst _resolvehref = __webpack_require__(/*! ../../../client/resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _isapiroute = __webpack_require__(/*! ../../../lib/is-api-route */ \"./node_modules/next/dist/lib/is-api-route.js\");\nconst _getnextpathnameinfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js\");\nconst _formatnextpathnameinfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js\");\nconst _comparestates = __webpack_require__(/*! ./utils/compare-states */ \"./node_modules/next/dist/shared/lib/router/utils/compare-states.js\");\nconst _islocalurl = __webpack_require__(/*! ./utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _isbot = __webpack_require__(/*! ./utils/is-bot */ \"./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _omit = __webpack_require__(/*! ./utils/omit */ \"./node_modules/next/dist/shared/lib/router/utils/omit.js\");\nconst _interpolateas = __webpack_require__(/*! ./utils/interpolate-as */ \"./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ./utils/handle-smooth-scroll */ \"./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nfunction buildCancellationError() {\n    return Object.assign(new Error(\"Route Cancelled\"), {\n        cancelled: true\n    });\n}\nasync function matchesMiddleware(options) {\n    const matchers = await Promise.resolve(options.router.pageLoader.getMiddleware());\n    if (!matchers) return false;\n    const { pathname: asPathname } = (0, _parsepath.parsePath)(options.asPath);\n    // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n    const cleanedAs = (0, _hasbasepath.hasBasePath)(asPathname) ? (0, _removebasepath.removeBasePath)(asPathname) : asPathname;\n    const asWithBasePathAndLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, options.locale));\n    // Check only path match on client. Matching \"has\" should be done on server\n    // where we can access more info such as headers, HttpOnly cookie, etc.\n    return matchers.some((m)=>new RegExp(m.regexp).test(asWithBasePathAndLocale));\n}\nfunction stripOrigin(url) {\n    const origin = (0, _utils.getLocationOrigin)();\n    return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction prepareUrlAs(router, url, as) {\n    // If url and as provided as an object representation,\n    // we'll format them into the string version here.\n    let [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, url, true);\n    const origin = (0, _utils.getLocationOrigin)();\n    const hrefWasAbsolute = resolvedHref.startsWith(origin);\n    const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin);\n    resolvedHref = stripOrigin(resolvedHref);\n    resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n    const preparedUrl = hrefWasAbsolute ? resolvedHref : (0, _addbasepath.addBasePath)(resolvedHref);\n    const preparedAs = as ? stripOrigin((0, _resolvehref.resolveHref)(router, as)) : resolvedAs || resolvedHref;\n    return {\n        url: preparedUrl,\n        as: asWasAbsolute ? preparedAs : (0, _addbasepath.addBasePath)(preparedAs)\n    };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n    const cleanPathname = (0, _removetrailingslash.removeTrailingSlash)((0, _denormalizepagepath.denormalizePagePath)(pathname));\n    if (cleanPathname === \"/404\" || cleanPathname === \"/_error\") {\n        return pathname;\n    }\n    // handle resolving href for dynamic routes\n    if (!pages.includes(cleanPathname)) {\n        // eslint-disable-next-line array-callback-return\n        pages.some((page)=>{\n            if ((0, _isdynamic.isDynamicRoute)(page) && (0, _routeregex.getRouteRegex)(page).re.test(cleanPathname)) {\n                pathname = page;\n                return true;\n            }\n        });\n    }\n    return (0, _removetrailingslash.removeTrailingSlash)(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n    const nextConfig = {\n        basePath: options.router.basePath,\n        i18n: {\n            locales: options.router.locales\n        },\n        trailingSlash: Boolean(false)\n    };\n    const rewriteHeader = response.headers.get(\"x-nextjs-rewrite\");\n    let rewriteTarget = rewriteHeader || response.headers.get(\"x-nextjs-matched-path\");\n    const matchedPath = response.headers.get(\"x-matched-path\");\n    if (matchedPath && !rewriteTarget && !matchedPath.includes(\"__next_data_catchall\") && !matchedPath.includes(\"/_error\") && !matchedPath.includes(\"/404\")) {\n        // leverage x-matched-path to detect next.config.js rewrites\n        rewriteTarget = matchedPath;\n    }\n    if (rewriteTarget) {\n        if (rewriteTarget.startsWith(\"/\") || undefined) {\n            const parsedRewriteTarget = (0, _parserelativeurl.parseRelativeUrl)(rewriteTarget);\n            const pathnameInfo = (0, _getnextpathnameinfo.getNextPathnameInfo)(parsedRewriteTarget.pathname, {\n                nextConfig,\n                parseData: true\n            });\n            let fsPathname = (0, _removetrailingslash.removeTrailingSlash)(pathnameInfo.pathname);\n            return Promise.all([\n                options.router.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)()\n            ]).then((param)=>{\n                let [pages, { __rewrites: rewrites }] = param;\n                let as = (0, _addlocale.addLocale)(pathnameInfo.pathname, pathnameInfo.locale);\n                if ((0, _isdynamic.isDynamicRoute)(as) || !rewriteHeader && pages.includes((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(as), options.router.locales).pathname)) {\n                    const parsedSource = (0, _getnextpathnameinfo.getNextPathnameInfo)((0, _parserelativeurl.parseRelativeUrl)(source).pathname, {\n                        nextConfig:  false ? 0 : nextConfig,\n                        parseData: true\n                    });\n                    as = (0, _addbasepath.addBasePath)(parsedSource.pathname);\n                    parsedRewriteTarget.pathname = as;\n                }\n                if (false) {} else if (!pages.includes(fsPathname)) {\n                    const resolvedPathname = resolveDynamicRoute(fsPathname, pages);\n                    if (resolvedPathname !== fsPathname) {\n                        fsPathname = resolvedPathname;\n                    }\n                }\n                const resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n                if ((0, _isdynamic.isDynamicRoute)(resolvedHref)) {\n                    const matches = (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(resolvedHref))(as);\n                    Object.assign(parsedRewriteTarget.query, matches || {});\n                }\n                return {\n                    type: \"rewrite\",\n                    parsedAs: parsedRewriteTarget,\n                    resolvedHref\n                };\n            });\n        }\n        const src = (0, _parsepath.parsePath)(source);\n        const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n            ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                nextConfig,\n                parseData: true\n            }),\n            defaultLocale: options.router.defaultLocale,\n            buildId: \"\"\n        });\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: \"\" + pathname + src.query + src.hash\n        });\n    }\n    const redirectTarget = response.headers.get(\"x-nextjs-redirect\");\n    if (redirectTarget) {\n        if (redirectTarget.startsWith(\"/\")) {\n            const src = (0, _parsepath.parsePath)(redirectTarget);\n            const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n                ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                    nextConfig,\n                    parseData: true\n                }),\n                defaultLocale: options.router.defaultLocale,\n                buildId: \"\"\n            });\n            return Promise.resolve({\n                type: \"redirect-internal\",\n                newAs: \"\" + pathname + src.query + src.hash,\n                newUrl: \"\" + pathname + src.query + src.hash\n            });\n        }\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: redirectTarget\n        });\n    }\n    return Promise.resolve({\n        type: \"next\"\n    });\n}\nasync function withMiddlewareEffects(options) {\n    const matches = await matchesMiddleware(options);\n    if (!matches || !options.fetchData) {\n        return null;\n    }\n    try {\n        const data = await options.fetchData();\n        const effect = await getMiddlewareData(data.dataHref, data.response, options);\n        return {\n            dataHref: data.dataHref,\n            json: data.json,\n            response: data.response,\n            text: data.text,\n            cacheKey: data.cacheKey,\n            effect\n        };\n    } catch (e) {\n        /**\n     * TODO: Revisit this in the future.\n     * For now we will not consider middleware data errors to be fatal.\n     * maybe we should revisit in the future.\n     */ return null;\n    }\n}\nconst manualScrollRestoration =  false && 0;\nconst SSG_DATA_NOT_FOUND = Symbol(\"SSG_DATA_NOT_FOUND\");\nfunction fetchRetry(url, attempts, options) {\n    return fetch(url, {\n        // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n        // Cookies may also be required for `getServerSideProps`.\n        //\n        // > `fetch` won’t send cookies, unless you set the credentials init\n        // > option.\n        // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n        //\n        // > For maximum browser compatibility when it comes to sending &\n        // > receiving cookies, always supply the `credentials: 'same-origin'`\n        // > option instead of relying on the default.\n        // https://github.com/github/fetch#caveats\n        credentials: \"same-origin\",\n        method: options.method || \"GET\",\n        headers: Object.assign({}, options.headers, {\n            \"x-nextjs-data\": \"1\"\n        })\n    }).then((response)=>{\n        return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n    });\n}\nfunction tryToParseAsJSON(text) {\n    try {\n        return JSON.parse(text);\n    } catch (error) {\n        return null;\n    }\n}\nfunction fetchNextData(param) {\n    let { dataHref, inflightCache, isPrefetch, hasMiddleware, isServerRender, parseJSON, persistCache, isBackground, unstable_skipClientCache } = param;\n    const { href: cacheKey } = new URL(dataHref, window.location.href);\n    var _params_method;\n    const getData = (params)=>fetchRetry(dataHref, isServerRender ? 3 : 1, {\n            headers: Object.assign({}, isPrefetch ? {\n                purpose: \"prefetch\"\n            } : {}, isPrefetch && hasMiddleware ? {\n                \"x-middleware-prefetch\": \"1\"\n            } : {}),\n            method: (_params_method = params == null ? void 0 : params.method) != null ? _params_method : \"GET\"\n        }).then((response)=>{\n            if (response.ok && (params == null ? void 0 : params.method) === \"HEAD\") {\n                return {\n                    dataHref,\n                    response,\n                    text: \"\",\n                    json: {},\n                    cacheKey\n                };\n            }\n            return response.text().then((text)=>{\n                if (!response.ok) {\n                    /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */ if (hasMiddleware && [\n                        301,\n                        302,\n                        307,\n                        308\n                    ].includes(response.status)) {\n                        return {\n                            dataHref,\n                            response,\n                            text,\n                            json: {},\n                            cacheKey\n                        };\n                    }\n                    if (response.status === 404) {\n                        var _tryToParseAsJSON;\n                        if ((_tryToParseAsJSON = tryToParseAsJSON(text)) == null ? void 0 : _tryToParseAsJSON.notFound) {\n                            return {\n                                dataHref,\n                                json: {\n                                    notFound: SSG_DATA_NOT_FOUND\n                                },\n                                response,\n                                text,\n                                cacheKey\n                            };\n                        }\n                    }\n                    const error = new Error(\"Failed to load static props\");\n                    /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */ if (!isServerRender) {\n                        (0, _routeloader.markAssetError)(error);\n                    }\n                    throw error;\n                }\n                return {\n                    dataHref,\n                    json: parseJSON ? tryToParseAsJSON(text) : null,\n                    response,\n                    text,\n                    cacheKey\n                };\n            });\n        }).then((data)=>{\n            if (!persistCache || \"development\" !== \"production\" || 0) {\n                delete inflightCache[cacheKey];\n            }\n            return data;\n        }).catch((err)=>{\n            if (!unstable_skipClientCache) {\n                delete inflightCache[cacheKey];\n            }\n            if (err.message === \"Failed to fetch\" || // firefox\n            err.message === \"NetworkError when attempting to fetch resource.\" || // safari\n            err.message === \"Load failed\") {\n                (0, _routeloader.markAssetError)(err);\n            }\n            throw err;\n        });\n    // when skipping client cache we wait to update\n    // inflight cache until successful data response\n    // this allows racing click event with fetching newer data\n    // without blocking navigation when stale data is available\n    if (unstable_skipClientCache && persistCache) {\n        return getData({}).then((data)=>{\n            inflightCache[cacheKey] = Promise.resolve(data);\n            return data;\n        });\n    }\n    if (inflightCache[cacheKey] !== undefined) {\n        return inflightCache[cacheKey];\n    }\n    return inflightCache[cacheKey] = getData(isBackground ? {\n        method: \"HEAD\"\n    } : {});\n}\nfunction createKey() {\n    return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation(param) {\n    let { url, router } = param;\n    // ensure we don't trigger a hard navigation to the same\n    // URL as this can end up with an infinite refresh\n    if (url === (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(router.asPath, router.locale))) {\n        throw new Error(\"Invariant: attempted to hard navigate to the same URL \" + url + \" \" + location.href);\n    }\n    window.location.href = url;\n}\nconst getCancelledHandler = (param)=>{\n    let { route, router } = param;\n    let cancelled = false;\n    const cancel = router.clc = ()=>{\n        cancelled = true;\n    };\n    const handleCancelled = ()=>{\n        if (cancelled) {\n            const error = new Error('Abort fetching component for route: \"' + route + '\"');\n            error.cancelled = true;\n            throw error;\n        }\n        if (cancel === router.clc) {\n            router.clc = null;\n        }\n    };\n    return handleCancelled;\n};\nclass Router {\n    reload() {\n        window.location.reload();\n    }\n    /**\n   * Go back in history\n   */ back() {\n        window.history.back();\n    }\n    /**\n   * Go forward in history\n   */ forward() {\n        window.history.forward();\n    }\n    /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ push(url, as, options) {\n        if (options === void 0) options = {};\n        if (false) {}\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change(\"pushState\", url, as, options);\n    }\n    /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ replace(url, as, options) {\n        if (options === void 0) options = {};\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change(\"replaceState\", url, as, options);\n    }\n    async _bfl(as, resolvedAs, locale, skipNavigate) {\n        if (true) {\n            let matchesBflStatic = false;\n            let matchesBflDynamic = false;\n            for (const curAs of [\n                as,\n                resolvedAs\n            ]){\n                if (curAs) {\n                    const asNoSlash = (0, _removetrailingslash.removeTrailingSlash)(new URL(curAs, \"http://n\").pathname);\n                    const asNoSlashLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asNoSlash, locale || this.locale));\n                    if (asNoSlash !== (0, _removetrailingslash.removeTrailingSlash)(new URL(this.asPath, \"http://n\").pathname)) {\n                        var _this__bfl_s, _this__bfl_s1;\n                        matchesBflStatic = matchesBflStatic || !!((_this__bfl_s = this._bfl_s) == null ? void 0 : _this__bfl_s.contains(asNoSlash)) || !!((_this__bfl_s1 = this._bfl_s) == null ? void 0 : _this__bfl_s1.contains(asNoSlashLocale));\n                        for (const normalizedAS of [\n                            asNoSlash,\n                            asNoSlashLocale\n                        ]){\n                            // if any sub-path of as matches a dynamic filter path\n                            // it should be hard navigated\n                            const curAsParts = normalizedAS.split(\"/\");\n                            for(let i = 0; !matchesBflDynamic && i < curAsParts.length + 1; i++){\n                                var _this__bfl_d;\n                                const currentPart = curAsParts.slice(0, i).join(\"/\");\n                                if (currentPart && ((_this__bfl_d = this._bfl_d) == null ? void 0 : _this__bfl_d.contains(currentPart))) {\n                                    matchesBflDynamic = true;\n                                    break;\n                                }\n                            }\n                        }\n                        // if the client router filter is matched then we trigger\n                        // a hard navigation\n                        if (matchesBflStatic || matchesBflDynamic) {\n                            if (skipNavigate) {\n                                return true;\n                            }\n                            handleHardNavigation({\n                                url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n                                router: this\n                            });\n                            return new Promise(()=>{});\n                        }\n                    }\n                }\n            }\n        }\n        return false;\n    }\n    async change(method, url, as, options, forcedScroll) {\n        var _this_components_pathname;\n        if (!(0, _islocalurl.isLocalURL)(url)) {\n            handleHardNavigation({\n                url,\n                router: this\n            });\n            return false;\n        }\n        // WARNING: `_h` is an internal option for handing Next.js client-side\n        // hydration. Your app should _never_ use this property. It may change at\n        // any time without notice.\n        const isQueryUpdating = options._h === 1;\n        if (!isQueryUpdating && !options.shallow) {\n            await this._bfl(as, undefined, options.locale);\n        }\n        let shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsepath.parsePath)(url).pathname === (0, _parsepath.parsePath)(as).pathname;\n        const nextState = {\n            ...this.state\n        };\n        // for static pages with query params in the URL we delay\n        // marking the router ready until after the query is updated\n        // or a navigation has occurred\n        const readyStateChange = this.isReady !== true;\n        this.isReady = true;\n        const isSsr = this.isSsr;\n        if (!isQueryUpdating) {\n            this.isSsr = false;\n        }\n        // if a route transition is already in progress before\n        // the query updating is triggered ignore query updating\n        if (isQueryUpdating && this.clc) {\n            return false;\n        }\n        const prevLocale = nextState.locale;\n        if (false) { var _this_locales; }\n        // marking route changes as a navigation start entry\n        if (_utils.ST) {\n            performance.mark(\"routeChange\");\n        }\n        const { shallow = false, scroll = true } = options;\n        const routeProps = {\n            shallow\n        };\n        if (this._inFlightRoute && this.clc) {\n            if (!isSsr) {\n                Router.events.emit(\"routeChangeError\", buildCancellationError(), this._inFlightRoute, routeProps);\n            }\n            this.clc();\n            this.clc = null;\n        }\n        as = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, options.locale, this.defaultLocale));\n        const cleanedAs = (0, _removelocale.removeLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, nextState.locale);\n        this._inFlightRoute = as;\n        const localeChange = prevLocale !== nextState.locale;\n        // If the url change is only related to a hash change\n        // We should not proceed. We should only change the state.\n        if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n            nextState.asPath = cleanedAs;\n            Router.events.emit(\"hashChangeStart\", as, routeProps);\n            // TODO: do we need the resolved href when only a hash change?\n            this.changeState(method, url, as, {\n                ...options,\n                scroll: false\n            });\n            if (scroll) {\n                this.scrollToHash(cleanedAs);\n            }\n            try {\n                await this.set(nextState, this.components[nextState.route], null);\n            } catch (err) {\n                if ((0, _iserror.default)(err) && err.cancelled) {\n                    Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                }\n                throw err;\n            }\n            Router.events.emit(\"hashChangeComplete\", as, routeProps);\n            return true;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        let { pathname, query } = parsed;\n        // if we detected the path as app route during prefetching\n        // trigger hard navigation\n        if ((_this_components_pathname = this.components[pathname]) == null ? void 0 : _this_components_pathname.__appRouter) {\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return new Promise(()=>{});\n        }\n        // The build manifest needs to be loaded before auto-static dynamic pages\n        // get their query parameters to allow ensuring they can be parsed properly\n        // when rewritten to\n        let pages, rewrites;\n        try {\n            [pages, { __rewrites: rewrites }] = await Promise.all([\n                this.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)(),\n                this.pageLoader.getMiddleware()\n            ]);\n        } catch (err) {\n            // If we fail to resolve the page list or client-build manifest, we must\n            // do a server-side transition:\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        // If asked to change the current URL we should reload the current page\n        // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n        // We also need to set the method = replaceState always\n        // as this should not go into the history (That's how browsers work)\n        // We should compare the new asPath to the current asPath, not the url\n        if (!this.urlIsNew(cleanedAs) && !localeChange) {\n            method = \"replaceState\";\n        }\n        // we need to resolve the as value using rewrites for dynamic SSG\n        // pages to allow building the data URL correctly\n        let resolvedAs = as;\n        // url and as should always be prefixed with basePath by this\n        // point by either next/link or router.push/replace so strip the\n        // basePath from the pathname to match the pages dir 1-to-1\n        pathname = pathname ? (0, _removetrailingslash.removeTrailingSlash)((0, _removebasepath.removeBasePath)(pathname)) : pathname;\n        let route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        const parsedAsPathname = as.startsWith(\"/\") && (0, _parserelativeurl.parseRelativeUrl)(as).pathname;\n        const isMiddlewareRewrite = !!(parsedAsPathname && route !== parsedAsPathname && (!(0, _isdynamic.isDynamicRoute)(route) || !(0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(route))(parsedAsPathname)));\n        // we don't attempt resolve asPath when we need to execute\n        // middleware as the resolving will occur server-side\n        const isMiddlewareMatch = !options.shallow && await matchesMiddleware({\n            asPath: as,\n            locale: nextState.locale,\n            router: this\n        });\n        if (isQueryUpdating && isMiddlewareMatch) {\n            shouldResolveHref = false;\n        }\n        if (shouldResolveHref && pathname !== \"/_error\") {\n            options._shouldResolveHref = true;\n            if (false) {} else {\n                parsed.pathname = resolveDynamicRoute(pathname, pages);\n                if (parsed.pathname !== pathname) {\n                    pathname = parsed.pathname;\n                    parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n                    if (!isMiddlewareMatch) {\n                        url = (0, _formaturl.formatWithValidation)(parsed);\n                    }\n                }\n            }\n        }\n        if (!(0, _islocalurl.isLocalURL)(as)) {\n            if (true) {\n                throw new Error('Invalid href: \"' + url + '\" and as: \"' + as + '\", received relative href and external as' + \"\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as\");\n            }\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(resolvedAs), nextState.locale);\n        route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        let routeMatch = false;\n        if ((0, _isdynamic.isDynamicRoute)(route)) {\n            const parsedAs = (0, _parserelativeurl.parseRelativeUrl)(resolvedAs);\n            const asPathname = parsedAs.pathname;\n            const routeRegex = (0, _routeregex.getRouteRegex)(route);\n            routeMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(asPathname);\n            const shouldInterpolate = route === asPathname;\n            const interpolatedAs = shouldInterpolate ? (0, _interpolateas.interpolateAs)(route, asPathname, query) : {};\n            if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n                const missingParams = Object.keys(routeRegex.groups).filter((param)=>!query[param] && !routeRegex.groups[param].optional);\n                if (missingParams.length > 0 && !isMiddlewareMatch) {\n                    if (true) {\n                        console.warn(\"\" + (shouldInterpolate ? \"Interpolating href\" : \"Mismatching `as` and `href`\") + \" failed to manually provide \" + (\"the params: \" + missingParams.join(\", \") + \" in the `href`'s `query`\"));\n                    }\n                    throw new Error((shouldInterpolate ? \"The provided `href` (\" + url + \") value is missing query values (\" + missingParams.join(\", \") + \") to be interpolated properly. \" : \"The provided `as` value (\" + asPathname + \") is incompatible with the `href` value (\" + route + \"). \") + (\"Read more: https://nextjs.org/docs/messages/\" + (shouldInterpolate ? \"href-interpolation-failed\" : \"incompatible-href-as\")));\n                }\n            } else if (shouldInterpolate) {\n                as = (0, _formaturl.formatWithValidation)(Object.assign({}, parsedAs, {\n                    pathname: interpolatedAs.result,\n                    query: (0, _omit.omit)(query, interpolatedAs.params)\n                }));\n            } else {\n                // Merge params into `query`, overwriting any specified in search\n                Object.assign(query, routeMatch);\n            }\n        }\n        if (!isQueryUpdating) {\n            Router.events.emit(\"routeChangeStart\", as, routeProps);\n        }\n        const isErrorRoute = this.pathname === \"/404\" || this.pathname === \"/_error\";\n        try {\n            var _self___NEXT_DATA___props_pageProps, _self___NEXT_DATA___props, _routeInfo_props;\n            let routeInfo = await this.getRouteInfo({\n                route,\n                pathname,\n                query,\n                as,\n                resolvedAs,\n                routeProps,\n                locale: nextState.locale,\n                isPreview: nextState.isPreview,\n                hasMiddleware: isMiddlewareMatch,\n                unstable_skipClientCache: options.unstable_skipClientCache,\n                isQueryUpdating: isQueryUpdating && !this.isFallback,\n                isMiddlewareRewrite\n            });\n            if (!isQueryUpdating && !options.shallow) {\n                await this._bfl(as, \"resolvedAs\" in routeInfo ? routeInfo.resolvedAs : undefined, nextState.locale);\n            }\n            if (\"route\" in routeInfo && isMiddlewareMatch) {\n                pathname = routeInfo.route || route;\n                route = pathname;\n                if (!routeProps.shallow) {\n                    query = Object.assign({}, routeInfo.query || {}, query);\n                }\n                const cleanedParsedPathname = (0, _hasbasepath.hasBasePath)(parsed.pathname) ? (0, _removebasepath.removeBasePath)(parsed.pathname) : parsed.pathname;\n                if (routeMatch && pathname !== cleanedParsedPathname) {\n                    Object.keys(routeMatch).forEach((key)=>{\n                        if (routeMatch && query[key] === routeMatch[key]) {\n                            delete query[key];\n                        }\n                    });\n                }\n                if ((0, _isdynamic.isDynamicRoute)(pathname)) {\n                    const prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(new URL(as, location.href).pathname, nextState.locale), true);\n                    let rewriteAs = prefixedAs;\n                    if ((0, _hasbasepath.hasBasePath)(rewriteAs)) {\n                        rewriteAs = (0, _removebasepath.removeBasePath)(rewriteAs);\n                    }\n                    if (false) {}\n                    const routeRegex = (0, _routeregex.getRouteRegex)(pathname);\n                    const curRouteMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(new URL(rewriteAs, location.href).pathname);\n                    if (curRouteMatch) {\n                        Object.assign(query, curRouteMatch);\n                    }\n                }\n            }\n            // If the routeInfo brings a redirect we simply apply it.\n            if (\"type\" in routeInfo) {\n                if (routeInfo.type === \"redirect-internal\") {\n                    return this.change(method, routeInfo.newUrl, routeInfo.newAs, options);\n                } else {\n                    handleHardNavigation({\n                        url: routeInfo.destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n            }\n            const component = routeInfo.Component;\n            if (component && component.unstable_scriptLoader) {\n                const scripts = [].concat(component.unstable_scriptLoader());\n                scripts.forEach((script)=>{\n                    (0, _script.handleClientScriptLoad)(script.props);\n                });\n            }\n            // handle redirect on client-transition\n            if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n                if (routeInfo.props.pageProps && routeInfo.props.pageProps.__N_REDIRECT) {\n                    // Use the destination from redirect without adding locale\n                    options.locale = false;\n                    const destination = routeInfo.props.pageProps.__N_REDIRECT;\n                    // check if destination is internal (resolves to a page) and attempt\n                    // client-navigation if it is falling back to hard navigation if\n                    // it's not\n                    if (destination.startsWith(\"/\") && routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n                        const parsedHref = (0, _parserelativeurl.parseRelativeUrl)(destination);\n                        parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n                        const { url: newUrl, as: newAs } = prepareUrlAs(this, destination, destination);\n                        return this.change(method, newUrl, newAs, options);\n                    }\n                    handleHardNavigation({\n                        url: destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n                nextState.isPreview = !!routeInfo.props.__N_PREVIEW;\n                // handle SSG data 404\n                if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n                    let notFoundRoute;\n                    try {\n                        await this.fetchComponent(\"/404\");\n                        notFoundRoute = \"/404\";\n                    } catch (_) {\n                        notFoundRoute = \"/_error\";\n                    }\n                    routeInfo = await this.getRouteInfo({\n                        route: notFoundRoute,\n                        pathname: notFoundRoute,\n                        query,\n                        as,\n                        resolvedAs,\n                        routeProps: {\n                            shallow: false\n                        },\n                        locale: nextState.locale,\n                        isPreview: nextState.isPreview,\n                        isNotFound: true\n                    });\n                    if (\"type\" in routeInfo) {\n                        throw new Error(\"Unexpected middleware effect on /404\");\n                    }\n                }\n            }\n            if (isQueryUpdating && this.pathname === \"/_error\" && ((_self___NEXT_DATA___props = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps = _self___NEXT_DATA___props.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps.statusCode) === 500 && ((_routeInfo_props = routeInfo.props) == null ? void 0 : _routeInfo_props.pageProps)) {\n                // ensure statusCode is still correct for static 500 page\n                // when updating query information\n                routeInfo.props.pageProps.statusCode = 500;\n            }\n            var _routeInfo_route;\n            // shallow routing is only allowed for same page URL changes.\n            const isValidShallowRoute = options.shallow && nextState.route === ((_routeInfo_route = routeInfo.route) != null ? _routeInfo_route : route);\n            var _options_scroll;\n            const shouldScroll = (_options_scroll = options.scroll) != null ? _options_scroll : !isQueryUpdating && !isValidShallowRoute;\n            const resetScroll = shouldScroll ? {\n                x: 0,\n                y: 0\n            } : null;\n            const upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n            // the new state that the router gonna set\n            const upcomingRouterState = {\n                ...nextState,\n                route,\n                pathname,\n                query,\n                asPath: cleanedAs,\n                isFallback: false\n            };\n            // When the page being rendered is the 404 page, we should only update the\n            // query parameters. Route changes here might add the basePath when it\n            // wasn't originally present. This is also why this block is before the\n            // below `changeState` call which updates the browser's history (changing\n            // the URL).\n            if (isQueryUpdating && isErrorRoute) {\n                var _self___NEXT_DATA___props_pageProps1, _self___NEXT_DATA___props1, _routeInfo_props1;\n                routeInfo = await this.getRouteInfo({\n                    route: this.pathname,\n                    pathname: this.pathname,\n                    query,\n                    as,\n                    resolvedAs,\n                    routeProps: {\n                        shallow: false\n                    },\n                    locale: nextState.locale,\n                    isPreview: nextState.isPreview,\n                    isQueryUpdating: isQueryUpdating && !this.isFallback\n                });\n                if (\"type\" in routeInfo) {\n                    throw new Error(\"Unexpected middleware effect on \" + this.pathname);\n                }\n                if (this.pathname === \"/_error\" && ((_self___NEXT_DATA___props1 = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps1 = _self___NEXT_DATA___props1.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps1.statusCode) === 500 && ((_routeInfo_props1 = routeInfo.props) == null ? void 0 : _routeInfo_props1.pageProps)) {\n                    // ensure statusCode is still correct for static 500 page\n                    // when updating query information\n                    routeInfo.props.pageProps.statusCode = 500;\n                }\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (err) {\n                    if ((0, _iserror.default)(err) && err.cancelled) {\n                        Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                    }\n                    throw err;\n                }\n                return true;\n            }\n            Router.events.emit(\"beforeHistoryChange\", as, routeProps);\n            this.changeState(method, url, as, options);\n            // for query updates we can skip it if the state is unchanged and we don't\n            // need to scroll\n            // https://github.com/vercel/next.js/issues/37139\n            const canSkipUpdating = isQueryUpdating && !upcomingScrollState && !readyStateChange && !localeChange && (0, _comparestates.compareRouterStates)(upcomingRouterState, this.state);\n            if (!canSkipUpdating) {\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (e) {\n                    if (e.cancelled) routeInfo.error = routeInfo.error || e;\n                    else throw e;\n                }\n                if (routeInfo.error) {\n                    if (!isQueryUpdating) {\n                        Router.events.emit(\"routeChangeError\", routeInfo.error, cleanedAs, routeProps);\n                    }\n                    throw routeInfo.error;\n                }\n                if (false) {}\n                if (!isQueryUpdating) {\n                    Router.events.emit(\"routeChangeComplete\", as, routeProps);\n                }\n                // A hash mark # is the optional last part of a URL\n                const hashRegex = /#.+$/;\n                if (shouldScroll && hashRegex.test(as)) {\n                    this.scrollToHash(as);\n                }\n            }\n            return true;\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.cancelled) {\n                return false;\n            }\n            throw err;\n        }\n    }\n    changeState(method, url, as, options) {\n        if (options === void 0) options = {};\n        if (true) {\n            if (typeof window.history === \"undefined\") {\n                console.error(\"Warning: window.history is not available.\");\n                return;\n            }\n            if (typeof window.history[method] === \"undefined\") {\n                console.error(\"Warning: window.history.\" + method + \" is not available\");\n                return;\n            }\n        }\n        if (method !== \"pushState\" || (0, _utils.getURL)() !== as) {\n            this._shallow = options.shallow;\n            window.history[method]({\n                url,\n                as,\n                options,\n                __N: true,\n                key: this._key = method !== \"pushState\" ? this._key : createKey()\n            }, // Passing the empty string here should be safe against future changes to the method.\n            // https://developer.mozilla.org/docs/Web/API/History/replaceState\n            \"\", as);\n        }\n    }\n    async handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n        console.error(err);\n        if (err.cancelled) {\n            // bubble up cancellation errors\n            throw err;\n        }\n        if ((0, _routeloader.isAssetError)(err) || loadErrorFail) {\n            Router.events.emit(\"routeChangeError\", err, as, routeProps);\n            // If we can't load the page it could be one of following reasons\n            //  1. Page doesn't exists\n            //  2. Page does exist in a different zone\n            //  3. Internal error while loading the page\n            // So, doing a hard reload is the proper way to deal with this.\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            // Changing the URL doesn't block executing the current code path.\n            // So let's throw a cancellation error stop the routing logic.\n            throw buildCancellationError();\n        }\n        try {\n            let props;\n            const { page: Component, styleSheets } = await this.fetchComponent(\"/_error\");\n            const routeInfo = {\n                props,\n                Component,\n                styleSheets,\n                err,\n                error: err\n            };\n            if (!routeInfo.props) {\n                try {\n                    routeInfo.props = await this.getInitialProps(Component, {\n                        err,\n                        pathname,\n                        query\n                    });\n                } catch (gipErr) {\n                    console.error(\"Error in error page `getInitialProps`: \", gipErr);\n                    routeInfo.props = {};\n                }\n            }\n            return routeInfo;\n        } catch (routeInfoErr) {\n            return this.handleRouteInfoError((0, _iserror.default)(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + \"\"), pathname, query, as, routeProps, true);\n        }\n    }\n    async getRouteInfo(param) {\n        let { route: requestedRoute, pathname, query, as, resolvedAs, routeProps, locale, hasMiddleware, isPreview, unstable_skipClientCache, isQueryUpdating, isMiddlewareRewrite, isNotFound } = param;\n        /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */ let route = requestedRoute;\n        try {\n            var _data_effect, _data_effect1, _data_effect2, _data_response;\n            const handleCancelled = getCancelledHandler({\n                route,\n                router: this\n            });\n            let existingInfo = this.components[route];\n            if (routeProps.shallow && existingInfo && this.route === route) {\n                return existingInfo;\n            }\n            if (hasMiddleware) {\n                existingInfo = undefined;\n            }\n            let cachedRouteInfo = existingInfo && !(\"initial\" in existingInfo) && \"development\" !== \"development\" ? 0 : undefined;\n            const isBackground = isQueryUpdating;\n            const fetchNextDataParams = {\n                dataHref: this.pageLoader.getDataHref({\n                    href: (0, _formaturl.formatWithValidation)({\n                        pathname,\n                        query\n                    }),\n                    skipInterpolation: true,\n                    asPath: isNotFound ? \"/404\" : resolvedAs,\n                    locale\n                }),\n                hasMiddleware: true,\n                isServerRender: this.isSsr,\n                parseJSON: true,\n                inflightCache: isBackground ? this.sbc : this.sdc,\n                persistCache: !isPreview,\n                isPrefetch: false,\n                unstable_skipClientCache,\n                isBackground\n            };\n            let data = isQueryUpdating && !isMiddlewareRewrite ? null : await withMiddlewareEffects({\n                fetchData: ()=>fetchNextData(fetchNextDataParams),\n                asPath: isNotFound ? \"/404\" : resolvedAs,\n                locale: locale,\n                router: this\n            }).catch((err)=>{\n                // we don't hard error during query updating\n                // as it's un-necessary and doesn't need to be fatal\n                // unless it is a fallback route and the props can't\n                // be loaded\n                if (isQueryUpdating) {\n                    return null;\n                }\n                throw err;\n            });\n            // when rendering error routes we don't apply middleware\n            // effects\n            if (data && (pathname === \"/_error\" || pathname === \"/404\")) {\n                data.effect = undefined;\n            }\n            if (isQueryUpdating) {\n                if (!data) {\n                    data = {\n                        json: self.__NEXT_DATA__.props\n                    };\n                } else {\n                    data.json = self.__NEXT_DATA__.props;\n                }\n            }\n            handleCancelled();\n            if ((data == null ? void 0 : (_data_effect = data.effect) == null ? void 0 : _data_effect.type) === \"redirect-internal\" || (data == null ? void 0 : (_data_effect1 = data.effect) == null ? void 0 : _data_effect1.type) === \"redirect-external\") {\n                return data.effect;\n            }\n            if ((data == null ? void 0 : (_data_effect2 = data.effect) == null ? void 0 : _data_effect2.type) === \"rewrite\") {\n                const resolvedRoute = (0, _removetrailingslash.removeTrailingSlash)(data.effect.resolvedHref);\n                const pages = await this.pageLoader.getPageList();\n                // during query updating the page must match although during\n                // client-transition a redirect that doesn't match a page\n                // can be returned and this should trigger a hard navigation\n                // which is valid for incremental migration\n                if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n                    route = resolvedRoute;\n                    pathname = data.effect.resolvedHref;\n                    query = {\n                        ...query,\n                        ...data.effect.parsedAs.query\n                    };\n                    resolvedAs = (0, _removebasepath.removeBasePath)((0, _normalizelocalepath.normalizeLocalePath)(data.effect.parsedAs.pathname, this.locales).pathname);\n                    // Check again the cache with the new destination.\n                    existingInfo = this.components[route];\n                    if (routeProps.shallow && existingInfo && this.route === route && !hasMiddleware) {\n                        // If we have a match with the current route due to rewrite,\n                        // we can copy the existing information to the rewritten one.\n                        // Then, we return the information along with the matched route.\n                        return {\n                            ...existingInfo,\n                            route\n                        };\n                    }\n                }\n            }\n            if ((0, _isapiroute.isAPIRoute)(route)) {\n                handleHardNavigation({\n                    url: as,\n                    router: this\n                });\n                return new Promise(()=>{});\n            }\n            const routeInfo = cachedRouteInfo || await this.fetchComponent(route).then((res)=>({\n                    Component: res.page,\n                    styleSheets: res.styleSheets,\n                    __N_SSG: res.mod.__N_SSG,\n                    __N_SSP: res.mod.__N_SSP\n                }));\n            if (true) {\n                const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"./node_modules/next/dist/compiled/react-is/index.js\");\n                if (!isValidElementType(routeInfo.Component)) {\n                    throw new Error('The default export is not a React Component in page: \"' + pathname + '\"');\n                }\n            }\n            const wasBailedPrefetch = data == null ? void 0 : (_data_response = data.response) == null ? void 0 : _data_response.headers.get(\"x-middleware-skip\");\n            const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n            // For non-SSG prefetches that bailed before sending data\n            // we clear the cache to fetch full response\n            if (wasBailedPrefetch && (data == null ? void 0 : data.dataHref)) {\n                delete this.sdc[data.dataHref];\n            }\n            const { props, cacheKey } = await this._getData(async ()=>{\n                if (shouldFetchData) {\n                    if ((data == null ? void 0 : data.json) && !wasBailedPrefetch) {\n                        return {\n                            cacheKey: data.cacheKey,\n                            props: data.json\n                        };\n                    }\n                    const dataHref = (data == null ? void 0 : data.dataHref) ? data.dataHref : this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname,\n                            query\n                        }),\n                        asPath: resolvedAs,\n                        locale\n                    });\n                    const fetched = await fetchNextData({\n                        dataHref,\n                        isServerRender: this.isSsr,\n                        parseJSON: true,\n                        inflightCache: wasBailedPrefetch ? {} : this.sdc,\n                        persistCache: !isPreview,\n                        isPrefetch: false,\n                        unstable_skipClientCache\n                    });\n                    return {\n                        cacheKey: fetched.cacheKey,\n                        props: fetched.json || {}\n                    };\n                }\n                return {\n                    headers: {},\n                    props: await this.getInitialProps(routeInfo.Component, {\n                        pathname,\n                        query,\n                        asPath: as,\n                        locale,\n                        locales: this.locales,\n                        defaultLocale: this.defaultLocale\n                    })\n                };\n            });\n            // Only bust the data cache for SSP routes although\n            // middleware can skip cache per request with\n            // x-middleware-cache: no-cache as well\n            if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n                delete this.sdc[cacheKey];\n            }\n            // we kick off a HEAD request in the background\n            // when a non-prefetch request is made to signal revalidation\n            if (!this.isPreview && routeInfo.__N_SSG && \"development\" !== \"development\" && 0) {}\n            props.pageProps = Object.assign({}, props.pageProps);\n            routeInfo.props = props;\n            routeInfo.route = route;\n            routeInfo.query = query;\n            routeInfo.resolvedAs = resolvedAs;\n            this.components[route] = routeInfo;\n            return routeInfo;\n        } catch (err) {\n            return this.handleRouteInfoError((0, _iserror.getProperError)(err), pathname, query, as, routeProps);\n        }\n    }\n    set(state, data, resetScroll) {\n        this.state = state;\n        return this.sub(data, this.components[\"/_app\"].Component, resetScroll);\n    }\n    /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */ beforePopState(cb) {\n        this._bps = cb;\n    }\n    onlyAHashChange(as) {\n        if (!this.asPath) return false;\n        const [oldUrlNoHash, oldHash] = this.asPath.split(\"#\", 2);\n        const [newUrlNoHash, newHash] = as.split(\"#\", 2);\n        // Makes sure we scroll to the provided hash if the url/hash are the same\n        if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n            return true;\n        }\n        // If the urls are change, there's more than a hash change\n        if (oldUrlNoHash !== newUrlNoHash) {\n            return false;\n        }\n        // If the hash has changed, then it's a hash only change.\n        // This check is necessary to handle both the enter and\n        // leave hash === '' cases. The identity case falls through\n        // and is treated as a next reload.\n        return oldHash !== newHash;\n    }\n    scrollToHash(as) {\n        const [, hash = \"\"] = as.split(\"#\", 2);\n        (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n            // Scroll to top if the hash is just `#` with no value or `#top`\n            // To mirror browsers\n            if (hash === \"\" || hash === \"top\") {\n                window.scrollTo(0, 0);\n                return;\n            }\n            // Decode hash to make non-latin anchor works.\n            const rawHash = decodeURIComponent(hash);\n            // First we check if the element by id is found\n            const idEl = document.getElementById(rawHash);\n            if (idEl) {\n                idEl.scrollIntoView();\n                return;\n            }\n            // If there's no element with the id, we check the `name` property\n            // To mirror browsers\n            const nameEl = document.getElementsByName(rawHash)[0];\n            if (nameEl) {\n                nameEl.scrollIntoView();\n            }\n        }, {\n            onlyHashChange: this.onlyAHashChange(as)\n        });\n    }\n    urlIsNew(asPath) {\n        return this.asPath !== asPath;\n    }\n    /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */ async prefetch(url, asPath, options) {\n        if (asPath === void 0) asPath = url;\n        if (options === void 0) options = {};\n        // Prefetch is not supported in development mode because it would trigger on-demand-entries\n        if (true) {\n            return;\n        }\n        if ( true && (0, _isbot.isBot)(window.navigator.userAgent)) {\n            // No prefetches for bots that render the link since they are typically navigating\n            // links via the equivalent of a hard navigation and hence never utilize these\n            // prefetches.\n            return;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        const urlPathname = parsed.pathname;\n        let { pathname, query } = parsed;\n        const originalPathname = pathname;\n        if (false) {}\n        const pages = await this.pageLoader.getPageList();\n        let resolvedAs = asPath;\n        const locale = typeof options.locale !== \"undefined\" ? options.locale || undefined : this.locale;\n        const isMiddlewareMatch = await matchesMiddleware({\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        if (false) {}\n        parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n        if ((0, _isdynamic.isDynamicRoute)(parsed.pathname)) {\n            pathname = parsed.pathname;\n            parsed.pathname = pathname;\n            Object.assign(query, (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(parsed.pathname))((0, _parsepath.parsePath)(asPath).pathname) || {});\n            if (!isMiddlewareMatch) {\n                url = (0, _formaturl.formatWithValidation)(parsed);\n            }\n        }\n        const data =  false ? 0 : await withMiddlewareEffects({\n            fetchData: ()=>fetchNextData({\n                    dataHref: this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname: originalPathname,\n                            query\n                        }),\n                        skipInterpolation: true,\n                        asPath: resolvedAs,\n                        locale\n                    }),\n                    hasMiddleware: true,\n                    isServerRender: this.isSsr,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true\n                }),\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */ if ((data == null ? void 0 : data.effect.type) === \"rewrite\") {\n            parsed.pathname = data.effect.resolvedHref;\n            pathname = data.effect.resolvedHref;\n            query = {\n                ...query,\n                ...data.effect.parsedAs.query\n            };\n            resolvedAs = data.effect.parsedAs.pathname;\n            url = (0, _formaturl.formatWithValidation)(parsed);\n        }\n        /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */ if ((data == null ? void 0 : data.effect.type) === \"redirect-external\") {\n            return;\n        }\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n            this.components[urlPathname] = {\n                __appRouter: true\n            };\n        }\n        await Promise.all([\n            this.pageLoader._isSsg(route).then((isSsg)=>{\n                return isSsg ? fetchNextData({\n                    dataHref: (data == null ? void 0 : data.json) ? data == null ? void 0 : data.dataHref : this.pageLoader.getDataHref({\n                        href: url,\n                        asPath: resolvedAs,\n                        locale: locale\n                    }),\n                    isServerRender: false,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true,\n                    unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n                }).then(()=>false).catch(()=>false) : false;\n            }),\n            this.pageLoader[options.priority ? \"loadPage\" : \"prefetch\"](route)\n        ]);\n    }\n    async fetchComponent(route) {\n        const handleCancelled = getCancelledHandler({\n            route,\n            router: this\n        });\n        try {\n            const componentResult = await this.pageLoader.loadPage(route);\n            handleCancelled();\n            return componentResult;\n        } catch (err) {\n            handleCancelled();\n            throw err;\n        }\n    }\n    _getData(fn) {\n        let cancelled = false;\n        const cancel = ()=>{\n            cancelled = true;\n        };\n        this.clc = cancel;\n        return fn().then((data)=>{\n            if (cancel === this.clc) {\n                this.clc = null;\n            }\n            if (cancelled) {\n                const err = new Error(\"Loading initial props cancelled\");\n                err.cancelled = true;\n                throw err;\n            }\n            return data;\n        });\n    }\n    _getFlightData(dataHref) {\n        // Do not cache RSC flight response since it's not a static resource\n        return fetchNextData({\n            dataHref,\n            isServerRender: true,\n            parseJSON: false,\n            inflightCache: this.sdc,\n            persistCache: false,\n            isPrefetch: false\n        }).then((param)=>{\n            let { text } = param;\n            return {\n                data: text\n            };\n        });\n    }\n    getInitialProps(Component, ctx) {\n        const { Component: App } = this.components[\"/_app\"];\n        const AppTree = this._wrapApp(App);\n        ctx.AppTree = AppTree;\n        return (0, _utils.loadGetInitialProps)(App, {\n            AppTree,\n            Component,\n            router: this,\n            ctx\n        });\n    }\n    get route() {\n        return this.state.route;\n    }\n    get pathname() {\n        return this.state.pathname;\n    }\n    get query() {\n        return this.state.query;\n    }\n    get asPath() {\n        return this.state.asPath;\n    }\n    get locale() {\n        return this.state.locale;\n    }\n    get isFallback() {\n        return this.state.isFallback;\n    }\n    get isPreview() {\n        return this.state.isPreview;\n    }\n    constructor(pathname, query, as, { initialProps, pageLoader, App, wrapApp, Component, err, subscription, isFallback, locale, locales, defaultLocale, domainLocales, isPreview }){\n        // Server Data Cache (full data requests)\n        this.sdc = {};\n        // Server Background Cache (HEAD requests)\n        this.sbc = {};\n        this.isFirstPopStateEvent = true;\n        this._key = createKey();\n        this.onPopState = (e)=>{\n            const { isFirstPopStateEvent } = this;\n            this.isFirstPopStateEvent = false;\n            const state = e.state;\n            if (!state) {\n                // We get state as undefined for two reasons.\n                //  1. With older safari (< 8) and older chrome (< 34)\n                //  2. When the URL changed with #\n                //\n                // In the both cases, we don't need to proceed and change the route.\n                // (as it's already changed)\n                // But we can simply replace the state with the new changes.\n                // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n                // So, doing the following for (1) does no harm.\n                const { pathname, query } = this;\n                this.changeState(\"replaceState\", (0, _formaturl.formatWithValidation)({\n                    pathname: (0, _addbasepath.addBasePath)(pathname),\n                    query\n                }), (0, _utils.getURL)());\n                return;\n            }\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            if (state.__NA) {\n                window.location.reload();\n                return;\n            }\n            if (!state.__N) {\n                return;\n            }\n            // Safari fires popstateevent when reopening the browser.\n            if (isFirstPopStateEvent && this.locale === state.options.locale && state.as === this.asPath) {\n                return;\n            }\n            let forcedScroll;\n            const { url, as, options, key } = state;\n            if (false) {}\n            this._key = key;\n            const { pathname } = (0, _parserelativeurl.parseRelativeUrl)(url);\n            // Make sure we don't re-render on initial load,\n            // can be caused by navigating back from an external site\n            if (this.isSsr && as === (0, _addbasepath.addBasePath)(this.asPath) && pathname === (0, _addbasepath.addBasePath)(this.pathname)) {\n                return;\n            }\n            // If the downstream application returns falsy, return.\n            // They will then be responsible for handling the event.\n            if (this._bps && !this._bps(state)) {\n                return;\n            }\n            this.change(\"replaceState\", url, as, Object.assign({}, options, {\n                shallow: options.shallow && this._shallow,\n                locale: options.locale || this.defaultLocale,\n                // @ts-ignore internal value not exposed on types\n                _h: 0\n            }), forcedScroll);\n        };\n        // represents the current component key\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        // set up the component cache (by route keys)\n        this.components = {};\n        // We should not keep the cache, if there's an error\n        // Otherwise, this cause issues when when going back and\n        // come again to the errored page.\n        if (pathname !== \"/_error\") {\n            this.components[route] = {\n                Component,\n                initial: true,\n                props: initialProps,\n                err,\n                __N_SSG: initialProps && initialProps.__N_SSG,\n                __N_SSP: initialProps && initialProps.__N_SSP\n            };\n        }\n        this.components[\"/_app\"] = {\n            Component: App,\n            styleSheets: []\n        };\n        if (true) {\n            const { BloomFilter } = __webpack_require__(/*! ../../lib/bloom-filter */ \"./node_modules/next/dist/shared/lib/bloom-filter.js\");\n            const staticFilterData = {\"numItems\":48,\"errorRate\":0.01,\"numBits\":461,\"numHashes\":7,\"bitArray\":[1,1,0,0,1,0,0,0,1,1,1,0,1,0,1,1,0,0,0,1,1,1,0,1,1,0,0,1,0,1,1,0,1,1,0,0,0,1,0,0,0,0,1,1,0,1,1,0,0,0,0,0,1,1,0,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,0,1,0,0,1,1,0,1,1,1,1,0,0,1,1,1,0,1,1,0,1,1,1,1,0,1,1,1,1,1,1,1,0,1,1,1,1,0,0,1,0,1,0,0,0,1,0,0,0,1,0,1,1,1,0,1,0,1,0,1,0,1,0,1,1,1,0,1,0,1,1,0,1,1,0,1,1,1,1,1,0,0,0,0,0,0,1,1,1,0,1,1,0,0,1,1,1,0,1,0,1,0,0,0,1,1,1,0,1,1,1,0,0,0,0,0,1,0,0,0,1,0,1,0,1,0,0,0,1,1,1,1,1,0,1,1,0,0,0,0,1,1,0,0,0,0,0,1,1,0,0,0,1,1,1,1,0,1,1,0,1,0,1,0,0,0,0,0,0,0,1,1,0,0,0,0,0,1,1,1,0,0,1,1,0,1,0,0,1,1,1,1,0,1,1,1,0,1,1,1,0,0,0,1,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,1,1,1,1,0,0,1,0,0,0,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,0,1,0,0,1,0,0,1,1,1,0,1,0,1,0,1,0,0,1,0,0,0,0,1,0,1,0,1,0,0,1,1,1,1,0,1,1,1,1,1,0,0,0,1,1,1,1,1,0,0,1,1,1,0,1,0,1,0,0,0,1,0,1,0,0,0,0,1,1,0,1,1,1,1,1,0,1,0,1,0,1,0,1,0,1,1,1,0,0,1,1,0,0,1,0,1,0,0,0,1,1,1,0,1,0,0,0,0,0,1,1,0,1,1,0,0,1,1,0,0,0,0,0,0,1,1,0,0,1,0,1,1,0,1,1,1,0,0,0,0,1,0]};\n            const dynamicFilterData = {\"numItems\":16,\"errorRate\":0.01,\"numBits\":154,\"numHashes\":7,\"bitArray\":[0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,1,0,1,0,0,1,1,1,1,0,0,0,1,1,1,0,1,1,0,1,0,0,1,1,1,1,1,1,0,1,0,1,1,0,1,0,0,1,1,1,1,0,0,1,1,1,1,0,0,1,0,0,0,0,1,1,0,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,0,1,0,0,1,1,1,1,0,0,1,0,0,0,0,1,0,1,0,0,1,0,1,1,1,1,0,0,1,1,1,0,0,1,0,1,1,0,0,1,0,1,0,0,1,1,0,1,1,1,1,0,1,1,0,0,1,0,0,0,1,1,1,0]};\n            if (staticFilterData == null ? void 0 : staticFilterData.numHashes) {\n                this._bfl_s = new BloomFilter(staticFilterData.numItems, staticFilterData.errorRate);\n                this._bfl_s.import(staticFilterData);\n            }\n            if (dynamicFilterData == null ? void 0 : dynamicFilterData.numHashes) {\n                this._bfl_d = new BloomFilter(dynamicFilterData.numItems, dynamicFilterData.errorRate);\n                this._bfl_d.import(dynamicFilterData);\n            }\n        }\n        // Backwards compat for Router.router.events\n        // TODO: Should be remove the following major version as it was never documented\n        this.events = Router.events;\n        this.pageLoader = pageLoader;\n        // if auto prerendered and dynamic route wait to update asPath\n        // until after mount to prevent hydration mismatch\n        const autoExportDynamic = (0, _isdynamic.isDynamicRoute)(pathname) && self.__NEXT_DATA__.autoExport;\n        this.basePath =  false || \"\";\n        this.sub = subscription;\n        this.clc = null;\n        this._wrapApp = wrapApp;\n        // make sure to ignore extra popState in safari on navigating\n        // back from external site\n        this.isSsr = true;\n        this.isLocaleDomain = false;\n        this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.isExperimentalCompile || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !false);\n        if (false) {}\n        this.state = {\n            route,\n            pathname,\n            query,\n            asPath: autoExportDynamic ? pathname : as,\n            isPreview: !!isPreview,\n            locale:  false ? 0 : undefined,\n            isFallback\n        };\n        this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n        if (true) {\n            // make sure \"as\" doesn't start with double slashes or else it can\n            // throw an error as it's considered invalid\n            if (!as.startsWith(\"//\")) {\n                // in order for `e.state` to work on the `onpopstate` event\n                // we have to register the initial route upon initialization\n                const options = {\n                    locale\n                };\n                const asPath = (0, _utils.getURL)();\n                this._initialMatchesMiddlewarePromise = matchesMiddleware({\n                    router: this,\n                    locale,\n                    asPath\n                }).then((matches)=>{\n                    options._shouldResolveHref = as !== pathname;\n                    this.changeState(\"replaceState\", matches ? asPath : (0, _formaturl.formatWithValidation)({\n                        pathname: (0, _addbasepath.addBasePath)(pathname),\n                        query\n                    }), asPath, options);\n                    return matches;\n                });\n            }\n            window.addEventListener(\"popstate\", this.onPopState);\n            // enable custom scroll restoration handling when available\n            // otherwise fallback to browser's default handling\n            if (false) {}\n        }\n    }\n}\nRouter.events = (0, _mitt.default)(); //# sourceMappingURL=router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/router.js\n"));

/***/ })

});
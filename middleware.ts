import { withAuth } from 'next-auth/middleware'

export default withAuth(
  function middleware(req) {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token }) => !!token,
    },
  }
)

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/admin/:path*',
    '/api/customers/:path*',
    '/api/leads/:path*',
    '/api/quotations/:path*',
    '/api/invoices/:path*',
    '/api/contracts/:path*',
    '/api/activities/:path*',
    '/api/users/:path*'
  ]
}

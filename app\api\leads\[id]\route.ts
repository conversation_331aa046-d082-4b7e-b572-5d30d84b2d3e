import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for lead update
const leadUpdateSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().optional().nullable(),
  status: z.enum(['NEW', 'CONTACTED', 'QUALIFIED', 'PROPOSAL', 'NEGOTIATION', 'CLOSED_WON', 'CLOSED_LOST']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  source: z.string().optional().nullable(),
  value: z.number().min(0).optional().nullable(),
  expectedCloseDate: z.string().optional().nullable(),
  customerId: z.string().optional().nullable(),
  contactName: z.string().optional().nullable(),
  contactEmail: z.string().email('Invalid email').optional().nullable(),
  contactPhone: z.string().optional().nullable(),
  company: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  tags: z.array(z.string()).optional()
})

// GET /api/leads/[id] - Get single lead
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const lead = await prisma.lead.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        createdBy: {
          select: { name: true, email: true }
        },
        quotations: {
          orderBy: { createdAt: 'desc' },
          take: 5,
          include: {
            createdBy: { select: { name: true } }
          }
        },
        activities: {
          orderBy: { createdAt: 'desc' },
          take: 10,
          include: {
            createdBy: { select: { name: true } }
          }
        },
        _count: {
          select: {
            activities: true,
            quotations: true
          }
        }
      }
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    return NextResponse.json(lead)
  } catch (error) {
    console.error('Error fetching lead:', error)
    return NextResponse.json(
      { error: 'Failed to fetch lead' },
      { status: 500 }
    )
  }
}

// PUT /api/leads/[id] - Update lead
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = leadUpdateSchema.parse(body)

    // Check if lead exists and belongs to user's company
    const existingLead = await prisma.lead.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      }
    })

    if (!existingLead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // Prepare update data
    const updateData: any = { ...validatedData }

    if (validatedData.expectedCloseDate) {
      updateData.expectedCloseDate = new Date(validatedData.expectedCloseDate)
    }

    const lead = await prisma.lead.update({
      where: { id: params.id },
      data: updateData,
      include: {
        customer: {
          select: { id: true, name: true, email: true, company: true }
        },
        createdBy: {
          select: { name: true, email: true }
        },
        _count: {
          select: {
            activities: true,
            quotations: true
          }
        }
      }
    })

    // Log activity if status changed
    if (validatedData.status && validatedData.status !== existingLead.status) {
      await prisma.activity.create({
        data: {
          type: 'STATUS_CHANGE',
          title: 'Lead Status Updated',
          description: `Lead status changed from ${existingLead.status} to ${validatedData.status}`,
          leadId: lead.id,
          customerId: lead.customerId,
          companyId: session.user.companyId!,
          createdById: session.user.id
        }
      })
    }

    return NextResponse.json(lead)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating lead:', error)
    return NextResponse.json(
      { error: 'Failed to update lead' },
      { status: 500 }
    )
  }
}

// DELETE /api/leads/[id] - Delete lead
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if lead exists and belongs to user's company
    const lead = await prisma.lead.findFirst({
      where: {
        id: params.id,
        companyId: session.user.companyId || undefined
      },
      include: {
        _count: {
          select: {
            quotations: true,
            activities: true
          }
        }
      }
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // Check if lead has related records
    if (lead._count.quotations > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete lead with existing quotations',
          details: lead._count
        },
        { status: 400 }
      )
    }

    await prisma.lead.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Lead deleted successfully' })
  } catch (error) {
    console.error('Error deleting lead:', error)
    return NextResponse.json(
      { error: 'Failed to delete lead' },
      { status: 500 }
    )
  }
}

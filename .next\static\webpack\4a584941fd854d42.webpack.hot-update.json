{"c": ["app/layout", "webpack"], "r": ["app/dashboard/contracts/page"], "m": ["(app-pages-browser)/./app/dashboard/contracts/page.tsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cproj%5Cnextjs-saas%5Capp%5Cdashboard%5Ccontracts%5Cpage.tsx&server=false!"]}